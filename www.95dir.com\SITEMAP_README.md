# 95分类目录 - 全新站点地图系统

## 概述

本系统提供了一个全新的、符合标准的站点地图生成功能，支持分页、缓存、多种内容类型等特性。

## 主要特性

### 1. 标准兼容
- 完全符合 sitemap.org 标准
- 使用标准XML命名空间：`http://www.sitemaps.org/schemas/sitemap/0.9`
- 支持站点地图索引文件

### 2. 分页支持
- 每个站点地图最多包含50,000个URL
- 自动分页处理大量数据
- 支持多页站点地图

### 3. 缓存机制
- 文件缓存系统，提高访问速度
- 可配置缓存时间（默认1小时）
- 支持手动清除缓存

### 4. 多种内容类型
- 网站站点地图：包含所有已审核网站
- 文章站点地图：包含所有已发布文章
- 分类站点地图：包含所有分类页面
- 页面站点地图：包含自定义页面和功能页面
- 标签站点地图：包含热门标签页面

## 文件结构

```
├── module/sitemap.php                    # 站点地图主模块
├── source/module/sitemap_cache.php       # 缓存管理类
├── system/sitemap_manage.php             # 管理页面
├── themes/system/sitemap_manage.html     # 管理模板
├── sitemap.xml                          # 静态索引文件
├── sitemap_test.php                     # 测试页面
└── data/cache/sitemap/                  # 缓存目录
```

## URL结构

### 主要访问URL
- **站点地图索引**: `https://www.95dir.com/?mod=sitemap`
- **网站站点地图**: `https://www.95dir.com/?mod=sitemap&type=websites&page=1`
- **文章站点地图**: `https://www.95dir.com/?mod=sitemap&type=articles&page=1`
- **分类站点地图**: `https://www.95dir.com/?mod=sitemap&type=categories`
- **页面站点地图**: `https://www.95dir.com/?mod=sitemap&type=pages`
- **标签站点地图**: `https://www.95dir.com/?mod=sitemap&type=tags`

### SEO友好URL（通过.htaccess重写）
- `https://www.95dir.com/sitemap.xml` → 站点地图索引
- `https://www.95dir.com/sitemap-websites.xml` → 网站站点地图第1页
- `https://www.95dir.com/sitemap-websites-2.xml` → 网站站点地图第2页

## 管理功能

### 管理页面
访问 `system/sitemap_manage.php` 可以：
- 查看站点地图统计信息
- 管理缓存（清除、查看状态）
- 快速访问各类型站点地图
- 重新生成站点地图

### 缓存管理
```php
// 获取缓存实例
$cache = get_sitemap_cache();

// 清除所有缓存
$cache->clear();

// 清除特定类型缓存
clear_sitemap_cache('websites');

// 获取缓存统计
$stats = $cache->getStats();
```

## 配置选项

在 `module/sitemap.php` 中可以配置：

```php
$sitemap_config = array(
    'max_urls_per_sitemap' => 50000,  // 每个站点地图最大URL数量
    'cache_time' => 3600,             // 缓存时间（秒）
    'enable_cache' => true,           // 是否启用缓存
    'cache_dir' => APP_PATH . 'data/cache/sitemap/',  // 缓存目录
);
```

## 兼容性

### 向后兼容
系统保持与旧版本URL的兼容性：
- `?mod=sitemap&type=webdir` → 重定向到网站站点地图
- `?mod=sitemap&type=article` → 重定向到文章站点地图
- `?mod=sitemap&type=category` → 重定向到分类站点地图

### 旧函数保留
原有的站点地图函数仍然保留在相应模块中，确保不影响现有功能。

## 性能优化

### 1. 分页处理
- 避免一次性加载大量数据
- 每页最多50,000个URL
- 减少内存使用

### 2. 缓存机制
- 文件缓存减少数据库查询
- 可配置缓存时间
- 支持缓存预热

### 3. SQL优化
- 使用索引字段排序
- 限制查询字段
- 批量处理数据

## 搜索引擎优化

### 1. 标准格式
- 符合sitemap.org标准
- 正确的XML格式
- 适当的优先级设置

### 2. 更新频率
- 首页：daily, priority 1.0
- 分类页：weekly, priority 0.8
- 网站详情：weekly, priority 0.6
- 文章详情：monthly, priority 0.5
- 标签页：weekly, priority 0.4

### 3. 自动提交
建议在robots.txt中添加：
```
Sitemap: https://www.95dir.com/sitemap.xml
```

## 测试和调试

### 测试页面
访问 `sitemap_test.php` 进行功能测试：
- 检查函数是否正确加载
- 测试数据库连接
- 验证URL生成
- 检查缓存功能

### 常见问题
1. **函数不存在**: 确保包含了必要的模块文件
2. **缓存目录不可写**: 检查目录权限
3. **XML格式错误**: 检查数据中是否有特殊字符
4. **内存不足**: 调整PHP内存限制或减少每页URL数量

## 维护建议

### 1. 定期清理
- 定期清除过期缓存
- 监控缓存目录大小
- 检查站点地图访问日志

### 2. 性能监控
- 监控站点地图生成时间
- 检查搜索引擎抓取情况
- 优化慢查询

### 3. 更新策略
- 内容更新后清除相关缓存
- 定期重新生成站点地图
- 监控搜索引擎收录情况

## 技术支持

如有问题，请检查：
1. PHP错误日志
2. 数据库连接状态
3. 文件权限设置
4. 缓存目录状态

---

**版本**: 1.0  
**更新时间**: 2025-08-03  
**兼容性**: PHP 5.6+, MySQL 5.5+
