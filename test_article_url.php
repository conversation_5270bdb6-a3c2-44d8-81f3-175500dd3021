<?php
// 测试文章URL生成
require_once('./source/include/function.php');

// 模拟选项设置
$options = array(
    'site_url' => 'https://www.95dir.com/',
    'site_root' => '/',
    'link_struct' => 0  // 默认URL结构
);

// 测试文章ID
$test_ids = array(122, 156, 100);

echo "测试文章URL生成：\n\n";

foreach ($test_ids as $art_id) {
    $url = get_article_url($art_id, true);
    echo "文章ID {$art_id}:\n";
    echo "  原始URL: {$url}\n";
    echo "  XML转义后: " . xml_escape($url) . "\n";
    echo "  时间格式: " . date('c', time()) . "\n";
    echo "\n";
}

// 测试XML输出格式
echo "XML输出示例：\n";
echo "<url>\n";
echo "<loc>" . xml_escape(get_article_url(122, true)) . "</loc>\n";
echo "<lastmod>" . date('c', time()) . "</lastmod>\n";
echo "<changefreq>weekly</changefreq>\n";
echo "<priority>0.5</priority>\n";
echo "</url>\n";
?>
