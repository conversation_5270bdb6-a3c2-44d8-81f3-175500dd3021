# 规范URL修复 - 解决重复网页问题

## 问题描述

根据访问日志分析，网站存在大量重复URL问题，导致Google索引编制出现"重复网页，用户未选定规范网页"的问题。

### 问题示例：
- `https://www.95dir.com/archives/201803-1.html` (正确的clean URL)
- `https://www.95dir.com/archives/201803-1.html?mod=archives` (带查询参数的重复URL)

这种情况导致同一内容可以通过多个URL访问，造成SEO问题。

## 解决方案

### 1. .htaccess重定向规则 (`.htaccess`)

添加了完整的301重定向规则，将所有带查询参数的clean URL重定向到纯clean URL：

```apache
# 规范化URL重定向 - 解决重复网页问题
# 归档页面重定向
RewriteCond %{QUERY_STRING} ^mod=archives(&.*)?$
RewriteRule ^archives/(\d+)-(\d+)\.html$ /archives/$1-$2.html? [R=301,L]

# 更新页面重定向
RewriteCond %{QUERY_STRING} ^mod=update(&.*)?$
RewriteRule ^update/(\d+)-(\d+)\.html$ /update/$1-$2.html? [R=301,L]

# ... 其他模块的重定向规则
```

### 2. PHP规范URL强制 (`source/module/rewrite.php`)

添加了 `enforce_canonical_url()` 函数，在PHP层面检查并重定向非规范URL：

```php
function enforce_canonical_url() {
    // 检查当前URL是否为规范URL
    // 如果不是，执行301重定向到规范URL
}
```

### 3. 规范URL生成函数 (`source/module/prelink.php`)

添加了 `get_canonical_url()` 函数，用于生成正确的规范URL：

```php
function get_canonical_url($module = '', $params = array(), $abs_path = true) {
    // 根据模块和参数生成规范的clean URL
}
```

### 4. 模板canonical标签更新

更新了以下模板文件的canonical标签：
- `themes/default/webdir.html`
- `themes/default/siteinfo.html`
- `themes/default/artinfo.html`

从：
```html
<link rel="canonical" href="{#$site_url#}?mod=webdir&cid={#$cate_id#}" />
```

改为：
```html
<link rel="canonical" href="{#get_canonical_url('webdir', ['cid' => $cate_id, 'page' => $page])#}" />
```

### 5. robots.txt优化 (`robots.txt`)

添加了规则禁止搜索引擎抓取带查询参数的clean URL：

```
# 禁止抓取带有mod参数的clean URL（避免重复内容）
Disallow: /archives/*.html?mod=*
Disallow: /update/*.html?mod=*
Disallow: /webdir/*.html?mod=*
# ... 其他规则
```

### 6. 主程序集成 (`index.php`)

在主程序中添加了规范URL强制检查：

```php
// 强制规范URL重定向 - 解决重复网页问题
enforce_canonical_url();
```

## 修复效果

### 预期效果：
1. **解决重复内容问题** - 所有带查询参数的clean URL将自动重定向到纯clean URL
2. **统一URL格式** - 确保每个内容只有一个规范URL
3. **提升SEO效果** - 避免权重分散，提高搜索引擎排名
4. **改善用户体验** - URL更简洁，更易分享

### 重定向示例：
- `https://www.95dir.com/archives/201803-1.html?mod=archives` → `https://www.95dir.com/archives/201803-1.html`
- `https://www.95dir.com/update/30-1.html?mod=update` → `https://www.95dir.com/update/30-1.html`
- `https://www.95dir.com/webdir/category/1.html?mod=webdir` → `https://www.95dir.com/webdir/category/1.html`

## 测试方法

### 1. 自动测试
访问测试页面：`http://your-domain.com/test_canonical_fix.php`

### 2. 手动测试
1. 访问带查询参数的URL，检查是否自动重定向
2. 查看页面源码，确认canonical标签正确
3. 使用Google Search Console检查重复内容问题

### 3. 工具测试
- 使用curl测试重定向：`curl -I "https://www.95dir.com/archives/201803-1.html?mod=archives"`
- 检查HTTP状态码是否为301
- 确认Location头指向正确的clean URL

## 注意事项

1. **缓存清理** - 修改后需要清理所有缓存（页面缓存、模板缓存等）
2. **搜索引擎更新** - Google重新索引需要时间，建议提交sitemap
3. **监控效果** - 使用Google Search Console监控重复内容问题的改善情况
4. **备份重要** - 修改前已备份相关文件

## 文件修改清单

- ✅ `.htaccess` - 添加重定向规则
- ✅ `source/module/rewrite.php` - 添加规范URL强制函数
- ✅ `source/module/prelink.php` - 添加规范URL生成函数
- ✅ `index.php` - 集成规范URL检查
- ✅ `themes/default/webdir.html` - 更新canonical标签
- ✅ `themes/default/siteinfo.html` - 更新canonical标签
- ✅ `themes/default/artinfo.html` - 更新canonical标签
- ✅ `robots.txt` - 优化搜索引擎抓取规则
- ✅ `test_canonical_fix.php` - 测试脚本（可删除）

## 维护建议

1. 定期检查Google Search Console中的重复内容报告
2. 监控网站访问日志，确保重定向正常工作
3. 新增模块时，记得添加相应的重定向规则
4. 定期更新sitemap，帮助搜索引擎理解URL结构

---

**修复完成时间：** 2025-08-02  
**修复状态：** ✅ 已完成  
**测试状态：** 🔄 待测试
