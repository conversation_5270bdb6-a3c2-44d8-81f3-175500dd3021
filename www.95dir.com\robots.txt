# 95目录网 - robots.txt
# 更新时间: 2025-08-02
# 说明: 针对谷歌索引编制问题优化的robots.txt，解决自动重定向问题

# ===========================================
# 通用搜索引擎规则
# ===========================================
User-agent: *

# 允许抓取根目录
Allow: /

# 允许抓取的重要目录
Allow: /themes/
Allow: /public/
Allow: /uploads/
Allow: /data/static/
Allow: /wailian/

# ===========================================
# 禁止抓取的目录和文件
# ===========================================

# 系统核心目录
Disallow: /source/
Disallow: /system/
Disallow: /config.php
Disallow: /install/
Disallow: /upgrade/

# 会员和管理区域
Disallow: /member/
Disallow: /admin/

# 缓存和临时文件
Disallow: /data/cache/
Disallow: /data/compile/
Disallow: /data/session/
Disallow: /data/dbbak/
Disallow: /data/sql/

# 禁止抓取的参数和可能导致重定向的URL
Disallow: /*?*action=*
Disallow: /*?*do=*
Disallow: /*?*step=*
Disallow: /*?*act=*
Disallow: /*?*op=*
Disallow: /*?*ajax=*
Disallow: /*?*debug=*
Disallow: /*&amp;*
Disallow: /*%26amp%3B*

# ===========================================
# 解决重复网页问题 - 禁止抓取带查询参数的clean URL
# ===========================================

# 禁止抓取带有mod参数的clean URL（避免重复内容）
Disallow: /archives/*.html?mod=*
Disallow: /update/*.html?mod=*
Disallow: /webdir/*.html?mod=*
Disallow: /article/*.html?mod=*
Disallow: /siteinfo/*.html?mod=*
Disallow: /artinfo/*.html?mod=*
Disallow: /linkinfo/*.html?mod=*
Disallow: /diypage/*.html?mod=*
Disallow: /rssfeed/*.html?mod=*
Disallow: /sitemap/*.html?mod=*

# 禁止抓取带有其他参数的clean URL
Disallow: /archives/*.html?*
Disallow: /update/*.html?*
Disallow: /webdir/*.html?*
Disallow: /article/*.html?*
Disallow: /siteinfo/*.html?*
Disallow: /artinfo/*.html?*
Disallow: /linkinfo/*.html?*
Disallow: /diypage/*.html?*
Disallow: /rssfeed/*.html?*
Disallow: /sitemap/*.html?*

# ===========================================
# 明确允许抓取的重要页面（优先clean URL）
# ===========================================

# 主入口文件
Allow: /index.php

# 优先允许clean URL格式（伪静态）
Allow: /webdir/
Allow: /article/
Allow: /siteinfo/
Allow: /artinfo/
Allow: /linkinfo/
Allow: /diypage/
Allow: /search/
Allow: /update/
Allow: /archives/
Allow: /rssfeed/
Allow: /sitemap/

# 模块页面（仅当没有clean URL时）
Allow: /index
Allow: /webdir
Allow: /article
Allow: /weblink
Allow: /category
Allow: /update
Allow: /archives
Allow: /top
Allow: /search
Allow: /feedback
Allow: /link
Allow: /rssfeed
Allow: /sitemap

# 查询参数形式（作为后备，但优先级较低）
Allow: /*?mod=index$
Allow: /*?mod=webdir$
Allow: /*?mod=article$
Allow: /*?mod=weblink$
Allow: /*?mod=category$
Allow: /*?mod=update$
Allow: /*?mod=archives$
Allow: /*?mod=top$
Allow: /*?mod=search$
Allow: /*?mod=feedback$
Allow: /*?mod=sitemap$
Allow: /*?mod=siteinfo&wid=*
Allow: /*?mod=artinfo&aid=*
Allow: /*?mod=linkinfo&lid=*
Allow: /*?mod=diypage&pid=*
Allow: /*?mod=rssfeed&*

# ===========================================
# 网站地图位置
# ===========================================
# 主站点地图（推荐使用极简版本，更稳定）
Sitemap: https://www.95dir.com/sitemap_minimal.php

# 备用站点地图
Sitemap: https://www.95dir.com/sitemap.xml
Sitemap: https://www.95dir.com/?mod=sitemap

# ===========================================
# 搜索引擎特定设置
# ===========================================

# 百度爬虫
User-agent: Baiduspider
Crawl-delay: 1
Allow: /

# Google爬虫
User-agent: Googlebot
Crawl-delay: 1
Allow: /

# 360搜索爬虫
User-agent: 360Spider
Crawl-delay: 2
Allow: /

# 搜狗爬虫
User-agent: Sogou web spider
Crawl-delay: 1
Allow: /

# 必应爬虫
User-agent: bingbot
Crawl-delay: 1
Allow: /

# 神马爬虫
User-agent: YisouSpider
Crawl-delay: 2
Allow: /

# 头条搜索爬虫
User-agent: Bytespider
Crawl-delay: 1
Allow: /

# 微信搜索爬虫
User-agent: WeChatBot
Crawl-delay: 1
Allow: /

# 夸克搜索爬虫
User-agent: QuarkBot
Crawl-delay: 1
Allow: /

# ===========================================
# 恶意爬虫屏蔽
# ===========================================

# 屏蔽恶意爬虫
User-agent: SemrushBot
Disallow: /

User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

# ===========================================
# 默认抓取延迟
# ===========================================
Crawl-delay: 1
