<?php
/**
 * 站点地图缓存管理类
 * 提供站点地图的缓存功能，包括文件缓存和内存缓存
 */

if (!defined('IN_IWEBDIR')) exit('Access Denied');

class SitemapCache {
    
    private $cache_dir;
    private $cache_time;
    private $enable_cache;
    
    public function __construct($config = array()) {
        $this->cache_dir = isset($config['cache_dir']) ? $config['cache_dir'] : APP_PATH . 'data/cache/sitemap/';
        $this->cache_time = isset($config['cache_time']) ? $config['cache_time'] : 3600;
        $this->enable_cache = isset($config['enable_cache']) ? $config['enable_cache'] : true;
        
        // 确保缓存目录存在
        if ($this->enable_cache && !is_dir($this->cache_dir)) {
            @mkdir($this->cache_dir, 0755, true);
        }
    }
    
    /**
     * 获取缓存文件路径
     */
    private function getCacheFile($key) {
        return $this->cache_dir . md5($key) . '.xml';
    }
    
    /**
     * 检查缓存是否存在且有效
     */
    public function isValid($key) {
        if (!$this->enable_cache) {
            return false;
        }
        
        $cache_file = $this->getCacheFile($key);
        
        if (!file_exists($cache_file)) {
            return false;
        }
        
        // 检查缓存是否过期
        if ((time() - filemtime($cache_file)) > $this->cache_time) {
            @unlink($cache_file);
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取缓存内容
     */
    public function get($key) {
        if (!$this->isValid($key)) {
            return false;
        }
        
        $cache_file = $this->getCacheFile($key);
        return file_get_contents($cache_file);
    }
    
    /**
     * 设置缓存内容
     */
    public function set($key, $content) {
        if (!$this->enable_cache) {
            return false;
        }
        
        $cache_file = $this->getCacheFile($key);
        return file_put_contents($cache_file, $content) !== false;
    }
    
    /**
     * 删除指定缓存
     */
    public function delete($key) {
        $cache_file = $this->getCacheFile($key);
        if (file_exists($cache_file)) {
            return @unlink($cache_file);
        }
        return true;
    }
    
    /**
     * 清除所有缓存
     */
    public function clear() {
        if (!is_dir($this->cache_dir)) {
            return true;
        }
        
        $files = glob($this->cache_dir . '*.xml');
        $success = true;
        
        foreach ($files as $file) {
            if (!@unlink($file)) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * 清除过期缓存
     */
    public function clearExpired() {
        if (!is_dir($this->cache_dir)) {
            return true;
        }
        
        $files = glob($this->cache_dir . '*.xml');
        $success = true;
        $current_time = time();
        
        foreach ($files as $file) {
            if (($current_time - filemtime($file)) > $this->cache_time) {
                if (!@unlink($file)) {
                    $success = false;
                }
            }
        }
        
        return $success;
    }
    
    /**
     * 获取缓存统计信息
     */
    public function getStats() {
        $stats = array(
            'cache_enabled' => $this->enable_cache,
            'cache_dir' => $this->cache_dir,
            'cache_time' => $this->cache_time,
            'total_files' => 0,
            'total_size' => 0,
            'expired_files' => 0,
            'valid_files' => 0
        );
        
        if (!is_dir($this->cache_dir)) {
            return $stats;
        }
        
        $files = glob($this->cache_dir . '*.xml');
        $current_time = time();
        
        foreach ($files as $file) {
            $stats['total_files']++;
            $stats['total_size'] += filesize($file);
            
            if (($current_time - filemtime($file)) > $this->cache_time) {
                $stats['expired_files']++;
            } else {
                $stats['valid_files']++;
            }
        }
        
        return $stats;
    }
    
    /**
     * 格式化文件大小
     */
    public function formatSize($bytes) {
        $units = array('B', 'KB', 'MB', 'GB');
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * 生成缓存键
     */
    public function generateKey($type, $params = array()) {
        $key_parts = array($type);
        
        foreach ($params as $param_key => $param_value) {
            $key_parts[] = $param_key . '_' . $param_value;
        }
        
        return implode('_', $key_parts);
    }
    
    /**
     * 输出缓存内容（如果存在）
     */
    public function output($key) {
        $content = $this->get($key);
        if ($content !== false) {
            header('Content-Type: application/xml; charset=utf-8');
            header('X-Sitemap-Cache: HIT');
            echo $content;
            return true;
        }
        
        header('X-Sitemap-Cache: MISS');
        return false;
    }
    
    /**
     * 开始输出缓冲并准备缓存
     */
    public function startBuffer() {
        ob_start();
    }
    
    /**
     * 结束输出缓冲并保存缓存
     */
    public function endBuffer($key) {
        $content = ob_get_contents();
        ob_end_clean();
        
        // 保存到缓存
        $this->set($key, $content);
        
        // 输出内容
        header('Content-Type: application/xml; charset=utf-8');
        header('X-Sitemap-Cache: MISS');
        echo $content;
        
        return $content;
    }
}

/**
 * 获取站点地图缓存实例
 */
function get_sitemap_cache($config = array()) {
    static $instance = null;
    
    if ($instance === null) {
        $default_config = array(
            'cache_dir' => APP_PATH . 'data/cache/sitemap/',
            'cache_time' => 3600,
            'enable_cache' => true
        );
        
        $config = array_merge($default_config, $config);
        $instance = new SitemapCache($config);
    }
    
    return $instance;
}

/**
 * 清除站点地图缓存的便捷函数
 */
function clear_sitemap_cache($type = 'all') {
    $cache = get_sitemap_cache();
    
    if ($type == 'all') {
        return $cache->clear();
    } else {
        // 清除特定类型的缓存
        $pattern = $cache->cache_dir . '*' . $type . '*.xml';
        $files = glob($pattern);
        $success = true;
        
        foreach ($files as $file) {
            if (!@unlink($file)) {
                $success = false;
            }
        }
        
        return $success;
    }
}

?>
