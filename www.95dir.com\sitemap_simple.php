<?php
/**
 * 简化版站点地图 - 用于解决白屏问题
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('IN_IWEBDIR', true);
define('APP_PATH', './');

// 基本初始化
try {
    require_once('./source/init.php');
} catch (Exception $e) {
    die('初始化失败: ' . $e->getMessage());
}

// 获取参数
$type = isset($_GET['type']) ? trim($_GET['type']) : 'index';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;

// 设置XML头
header('Content-Type: application/xml; charset=utf-8');

// 生成站点地图
try {
    if ($type == 'index') {
        generate_simple_index();
    } elseif ($type == 'websites') {
        generate_simple_websites($page);
    } elseif ($type == 'articles') {
        generate_simple_articles($page);
    } else {
        generate_simple_index();
    }
} catch (Exception $e) {
    // 如果出错，输出错误信息
    header('Content-Type: text/html; charset=utf-8');
    echo '<h1>站点地图错误</h1>';
    echo '<p>错误信息: ' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '<p><a href="sitemap_test.php">返回测试页面</a></p>';
}

/**
 * 生成简单的站点地图索引
 */
function generate_simple_index() {
    global $options;
    
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    $base_url = rtrim($options['site_url'], '/');
    $current_time = date('c');
    
    // 网站站点地图
    echo "    <sitemap>\n";
    echo "        <loc>" . htmlspecialchars($base_url . '?mod=sitemap&type=websites', ENT_QUOTES, 'UTF-8') . "</loc>\n";
    echo "        <lastmod>" . $current_time . "</lastmod>\n";
    echo "    </sitemap>\n";
    
    // 文章站点地图
    echo "    <sitemap>\n";
    echo "        <loc>" . htmlspecialchars($base_url . '?mod=sitemap&type=articles', ENT_QUOTES, 'UTF-8') . "</loc>\n";
    echo "        <lastmod>" . $current_time . "</lastmod>\n";
    echo "    </sitemap>\n";
    
    echo '</sitemapindex>' . "\n";
}

/**
 * 生成简单的网站站点地图
 */
function generate_simple_websites($page = 1) {
    global $DB, $options;
    
    $limit = 1000; // 限制数量避免内存问题
    $offset = ($page - 1) * $limit;
    
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    // 首页
    if ($page == 1) {
        echo "    <url>\n";
        echo "        <loc>" . htmlspecialchars($options['site_url'], ENT_QUOTES, 'UTF-8') . "</loc>\n";
        echo "        <lastmod>" . date('c') . "</lastmod>\n";
        echo "        <changefreq>daily</changefreq>\n";
        echo "        <priority>1.0</priority>\n";
        echo "    </url>\n";
    }
    
    // 获取网站数据
    $where = "web_status=3";
    
    // 检查违规状态字段是否存在
    $table_name = $DB->table('websites');
    $check_sql = "SHOW COLUMNS FROM `{$table_name}` LIKE 'web_violation_status'";
    $check_result = $DB->query($check_sql);
    if ($DB->num_rows($check_result) > 0) {
        $where .= " AND (web_violation_status IS NULL OR web_violation_status=0)";
    }
    
    $sql = "SELECT web_id, web_name, web_url, web_ctime FROM " . $DB->table('websites') . 
           " WHERE $where ORDER BY web_id DESC LIMIT $offset, $limit";
    
    $query = $DB->query($sql);
    while ($row = $DB->fetch_array($query)) {
        $web_url = generate_website_url($row['web_id'], $row['web_name'], $row['web_url']);
        
        echo "    <url>\n";
        echo "        <loc>" . htmlspecialchars($web_url, ENT_QUOTES, 'UTF-8') . "</loc>\n";
        echo "        <lastmod>" . date('c', $row['web_ctime']) . "</lastmod>\n";
        echo "        <changefreq>weekly</changefreq>\n";
        echo "        <priority>0.6</priority>\n";
        echo "    </url>\n";
    }
    $DB->free_result($query);
    
    echo '</urlset>' . "\n";
}

/**
 * 生成简单的文章站点地图
 */
function generate_simple_articles($page = 1) {
    global $DB, $options;
    
    $limit = 1000;
    $offset = ($page - 1) * $limit;
    
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    $sql = "SELECT art_id, art_title, art_ctime FROM " . $DB->table('articles') . 
           " WHERE art_status=3 ORDER BY art_id DESC LIMIT $offset, $limit";
    
    $query = $DB->query($sql);
    while ($row = $DB->fetch_array($query)) {
        $art_url = generate_article_url($row['art_id'], $row['art_title']);
        
        echo "    <url>\n";
        echo "        <loc>" . htmlspecialchars($art_url, ENT_QUOTES, 'UTF-8') . "</loc>\n";
        echo "        <lastmod>" . date('c', $row['art_ctime']) . "</lastmod>\n";
        echo "        <changefreq>monthly</changefreq>\n";
        echo "        <priority>0.5</priority>\n";
        echo "    </url>\n";
    }
    $DB->free_result($query);
    
    echo '</urlset>' . "\n";
}

/**
 * 简单的网站URL生成
 */
function generate_website_url($web_id, $web_name = '', $web_url = '') {
    global $options;
    
    // 尝试使用现有函数
    if (function_exists('get_website_url')) {
        return get_website_url($web_id, true, $web_name, $web_url);
    }
    
    // 后备方案
    return $options['site_url'] . '?mod=siteinfo&wid=' . $web_id;
}

/**
 * 简单的文章URL生成
 */
function generate_article_url($art_id, $art_title = '') {
    global $options;
    
    // 尝试使用现有函数
    if (function_exists('get_article_url')) {
        return get_article_url($art_id, true, $art_title);
    }
    
    // 后备方案
    return $options['site_url'] . '?mod=artinfo&aid=' . $art_id;
}

// 尝试包含URL生成函数（可选）
if (file_exists('./source/module/prelink.php')) {
    @include_once('./source/module/prelink.php');
}

?>
