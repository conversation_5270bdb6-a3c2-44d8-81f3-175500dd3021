<?php
/**
 * 极简站点地图 - 不依赖框架
 */

// 错误处理
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 数据库配置 - 直接从config.php读取
if (file_exists('./config.php')) {
    include './config.php';
} else {
    die('Config file not found');
}

// 直接连接数据库
$mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($mysqli->connect_error) {
    die('Database connection failed: ' . $mysqli->connect_error);
}

// 设置字符集
$mysqli->set_charset('utf8');

// 获取站点URL - 简单方式
$site_url = 'https://www.95dir.com/';

// 获取参数
$type = isset($_GET['type']) ? $_GET['type'] : 'index';

// 设置XML头
header('Content-Type: application/xml; charset=utf-8');

// 生成站点地图
if ($type == 'index') {
    // 站点地图索引
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    echo "    <sitemap>\n";
    echo "        <loc>" . htmlspecialchars($site_url . '?mod=sitemap&amp;type=websites') . "</loc>\n";
    echo "        <lastmod>" . date('c') . "</lastmod>\n";
    echo "    </sitemap>\n";
    
    echo "    <sitemap>\n";
    echo "        <loc>" . htmlspecialchars($site_url . '?mod=sitemap&amp;type=articles') . "</loc>\n";
    echo "        <lastmod>" . date('c') . "</lastmod>\n";
    echo "    </sitemap>\n";
    
    echo '</sitemapindex>' . "\n";
    
} elseif ($type == 'websites') {
    // 网站站点地图
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    // 首页
    echo "    <url>\n";
    echo "        <loc>" . htmlspecialchars($site_url) . "</loc>\n";
    echo "        <lastmod>" . date('c') . "</lastmod>\n";
    echo "        <changefreq>daily</changefreq>\n";
    echo "        <priority>1.0</priority>\n";
    echo "    </url>\n";
    
    // 查询网站
    $sql = "SELECT web_id, web_name, web_ctime FROM " . TABLE_PREFIX . "websites WHERE web_status=3 ORDER BY web_id DESC LIMIT 1000";
    $result = $mysqli->query($sql);
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $web_url = $site_url . '?mod=siteinfo&wid=' . $row['web_id'];
            
            echo "    <url>\n";
            echo "        <loc>" . htmlspecialchars($web_url) . "</loc>\n";
            echo "        <lastmod>" . date('c', $row['web_ctime']) . "</lastmod>\n";
            echo "        <changefreq>weekly</changefreq>\n";
            echo "        <priority>0.6</priority>\n";
            echo "    </url>\n";
        }
        $result->free();
    }
    
    echo '</urlset>' . "\n";
    
} elseif ($type == 'articles') {
    // 文章站点地图
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    // 查询文章
    $sql = "SELECT art_id, art_title, art_ctime FROM " . TABLE_PREFIX . "articles WHERE art_status=3 ORDER BY art_id DESC LIMIT 1000";
    $result = $mysqli->query($sql);
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $art_url = $site_url . '?mod=artinfo&aid=' . $row['art_id'];
            
            echo "    <url>\n";
            echo "        <loc>" . htmlspecialchars($art_url) . "</loc>\n";
            echo "        <lastmod>" . date('c', $row['art_ctime']) . "</lastmod>\n";
            echo "        <changefreq>monthly</changefreq>\n";
            echo "        <priority>0.5</priority>\n";
            echo "    </url>\n";
        }
        $result->free();
    }
    
    echo '</urlset>' . "\n";
    
} else {
    // 默认重定向到索引
    header('Location: ?type=index');
    exit;
}

// 关闭数据库连接
$mysqli->close();
?>
