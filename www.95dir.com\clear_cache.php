<?php
/**
 * 清理缓存脚本 - 解决谷歌索引编制问题
 * 清理所有模板编译缓存和页面缓存，确保URL修复生效
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');

// 简单的安全检查
$allowed_ips = array('127.0.0.1', '::1', 'localhost');
$client_ip = $_SERVER['REMOTE_ADDR'] ?? '';

// 如果不是本地访问，需要验证
if (!in_array($client_ip, $allowed_ips)) {
    $auth_key = $_GET['key'] ?? '';
    if ($auth_key !== 'clear_cache_for_google_fix') {
        exit('Access Denied. Use: ?key=clear_cache_for_google_fix');
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>清理缓存 - 修复谷歌索引编制问题</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .progress { margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>清理缓存 - 修复谷歌索引编制问题</h1>
        
        <?php
        $start_time = microtime(true);
        $cleared_files = 0;
        $errors = array();
        
        echo '<div class="log">';
        echo '<h3>开始清理缓存...</h3>';
        
        // 1. 清理模板编译缓存
        echo '<div class="progress"><strong>1. 清理模板编译缓存</strong></div>';
        $compile_dir = ROOT_PATH . 'data/compile/';
        if (is_dir($compile_dir)) {
            $compile_files = glob($compile_dir . '*/*.php');
            foreach ($compile_files as $file) {
                if (is_file($file)) {
                    if (unlink($file)) {
                        $cleared_files++;
                        echo '<span class="success">✓</span> 删除: ' . basename($file) . '<br>';
                    } else {
                        $errors[] = '无法删除: ' . $file;
                        echo '<span class="error">✗</span> 失败: ' . basename($file) . '<br>';
                    }
                }
            }
        } else {
            echo '<span class="warning">⚠</span> 编译目录不存在<br>';
        }
        
        // 2. 清理页面缓存
        echo '<div class="progress"><strong>2. 清理页面缓存</strong></div>';
        $cache_dir = ROOT_PATH . 'data/cache/';
        if (is_dir($cache_dir)) {
            $cache_files = glob($cache_dir . '*.html');
            foreach ($cache_files as $file) {
                if (is_file($file)) {
                    if (unlink($file)) {
                        $cleared_files++;
                        echo '<span class="success">✓</span> 删除: ' . basename($file) . '<br>';
                    } else {
                        $errors[] = '无法删除: ' . $file;
                        echo '<span class="error">✗</span> 失败: ' . basename($file) . '<br>';
                    }
                }
            }
        } else {
            echo '<span class="warning">⚠</span> 缓存目录不存在<br>';
        }
        
        // 3. 清理Smarty缓存
        echo '<div class="progress"><strong>3. 清理Smarty缓存</strong></div>';
        $smarty_cache_dirs = array(
            ROOT_PATH . 'data/cache/default/',
            ROOT_PATH . 'data/cache/system/',
        );
        
        foreach ($smarty_cache_dirs as $dir) {
            if (is_dir($dir)) {
                $smarty_files = glob($dir . '*');
                foreach ($smarty_files as $file) {
                    if (is_file($file)) {
                        if (unlink($file)) {
                            $cleared_files++;
                            echo '<span class="success">✓</span> 删除: ' . basename($file) . '<br>';
                        } else {
                            $errors[] = '无法删除: ' . $file;
                            echo '<span class="error">✗</span> 失败: ' . basename($file) . '<br>';
                        }
                    }
                }
            }
        }
        
        // 4. 清理临时文件
        echo '<div class="progress"><strong>4. 清理临时文件</strong></div>';
        $temp_patterns = array(
            ROOT_PATH . 'data/temp/*',
            ROOT_PATH . 'data/log/*.log',
        );
        
        foreach ($temp_patterns as $pattern) {
            $temp_files = glob($pattern);
            foreach ($temp_files as $file) {
                if (is_file($file) && basename($file) !== '.htaccess') {
                    if (unlink($file)) {
                        $cleared_files++;
                        echo '<span class="success">✓</span> 删除: ' . basename($file) . '<br>';
                    } else {
                        $errors[] = '无法删除: ' . $file;
                        echo '<span class="error">✗</span> 失败: ' . basename($file) . '<br>';
                    }
                }
            }
        }
        
        $end_time = microtime(true);
        $execution_time = round($end_time - $start_time, 2);
        
        echo '<div class="progress"><strong>清理完成</strong></div>';
        echo '<div class="success">';
        echo '<h3>清理结果</h3>';
        echo '<p>✓ 成功清理 <strong>' . $cleared_files . '</strong> 个文件</p>';
        echo '<p>✓ 执行时间: <strong>' . $execution_time . '</strong> 秒</p>';
        echo '</div>';
        
        if (!empty($errors)) {
            echo '<div class="error">';
            echo '<h3>错误信息</h3>';
            foreach ($errors as $error) {
                echo '<p>✗ ' . htmlspecialchars($error) . '</p>';
            }
            echo '</div>';
        }
        
        echo '</div>';
        ?>
        
        <div class="info">
            <h3>修复说明</h3>
            <p>本次清理主要解决以下问题：</p>
            <ul>
                <li>清理包含 <code>&amp;</code> 实体编码的编译模板</li>
                <li>清理可能导致重定向的缓存页面</li>
                <li>强制重新生成所有URL链接</li>
                <li>确保搜索引擎爬虫能正确访问页面</li>
            </ul>
        </div>
        
        <div class="warning">
            <h3>后续操作建议</h3>
            <ol>
                <li><strong>测试URL:</strong> 访问 <a href="test_url_fix.php" target="_blank">test_url_fix.php</a> 验证修复效果</li>
                <li><strong>Google Search Console:</strong> 请求重新抓取问题页面</li>
                <li><strong>监控访问:</strong> 检查服务器日志确认Googlebot正常访问</li>
                <li><strong>等待索引:</strong> 通常24-48小时内会看到改善</li>
            </ol>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <p>
                <a href="test_url_fix.php">测试修复效果</a> | 
                <a href="system/url_fix.php">URL修复工具</a> | 
                <a href="./">返回首页</a>
            </p>
        </div>
        
        <div class="info">
            <h3>技术说明</h3>
            <p>本次修复主要针对以下技术问题：</p>
            <ul>
                <li><strong>URL重写函数:</strong> 移除了 <code>[&amp;|&]</code> 模式匹配，改为先转换再匹配</li>
                <li><strong>搜索引擎检测:</strong> 添加了爬虫检测，为不同用户输出不同格式的URL</li>
                <li><strong>HTML实体处理:</strong> 确保href属性中的URL参数正确</li>
                <li><strong>缓存清理:</strong> 清理所有可能包含错误URL的缓存文件</li>
            </ul>
        </div>
    </div>
</body>
</html>
