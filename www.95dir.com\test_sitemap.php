<?php
/**
 * 站点地图功能测试脚本
 */
require_once('./source/init.php');
require_once('./source/module/sitemap_cache.php');

// 包含必要的模块文件
require_once('./source/module/prelink.php');
require_once('./source/module/category.php');
require_once('./source/module/website.php');
require_once('./source/module/article.php');

echo "<h1>站点地图功能测试</h1>\n";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:4px;}</style>\n";

// 测试1: 检查缓存目录
echo "<h2>1. 缓存目录测试</h2>\n";
$cache_dir = APP_PATH . 'data/cache/sitemap/';
if (is_dir($cache_dir)) {
    echo "<p class='success'>✓ 缓存目录存在: {$cache_dir}</p>\n";
    if (is_writable($cache_dir)) {
        echo "<p class='success'>✓ 缓存目录可写</p>\n";
    } else {
        echo "<p class='error'>✗ 缓存目录不可写</p>\n";
    }
} else {
    echo "<p class='info'>缓存目录不存在，尝试创建...</p>\n";
    if (@mkdir($cache_dir, 0755, true)) {
        echo "<p class='success'>✓ 缓存目录创建成功</p>\n";
    } else {
        echo "<p class='error'>✗ 缓存目录创建失败</p>\n";
    }
}

// 测试2: 检查数据库连接和数据
echo "<h2>2. 数据库测试</h2>\n";
try {
    $websites_count = $DB->get_count($DB->table('websites'), "web_status=3");
    $articles_count = $DB->get_count($DB->table('articles'), "art_status=3");
    $categories_count = $DB->get_count($DB->table('categories'), "1");
    
    echo "<p class='success'>✓ 数据库连接正常</p>\n";
    echo "<p class='info'>网站数量: {$websites_count}</p>\n";
    echo "<p class='info'>文章数量: {$articles_count}</p>\n";
    echo "<p class='info'>分类数量: {$categories_count}</p>\n";
} catch (Exception $e) {
    echo "<p class='error'>✗ 数据库连接失败: " . $e->getMessage() . "</p>\n";
}

// 测试3: 测试缓存类
echo "<h2>3. 缓存类测试</h2>\n";
try {
    $cache = get_sitemap_cache();
    echo "<p class='success'>✓ 缓存类实例化成功</p>\n";
    
    // 测试缓存写入和读取
    $test_key = 'test_cache_' . time();
    $test_content = '<?xml version="1.0" encoding="UTF-8"?><test>Hello World</test>';
    
    if ($cache->set($test_key, $test_content)) {
        echo "<p class='success'>✓ 缓存写入成功</p>\n";
        
        if ($cache->isValid($test_key)) {
            echo "<p class='success'>✓ 缓存验证成功</p>\n";
            
            $retrieved_content = $cache->get($test_key);
            if ($retrieved_content === $test_content) {
                echo "<p class='success'>✓ 缓存读取成功</p>\n";
            } else {
                echo "<p class='error'>✗ 缓存内容不匹配</p>\n";
            }
        } else {
            echo "<p class='error'>✗ 缓存验证失败</p>\n";
        }
        
        // 清理测试缓存
        $cache->delete($test_key);
        echo "<p class='info'>测试缓存已清理</p>\n";
    } else {
        echo "<p class='error'>✗ 缓存写入失败</p>\n";
    }
    
    // 显示缓存统计
    $stats = $cache->getStats();
    echo "<p class='info'>缓存统计:</p>\n";
    echo "<pre>" . print_r($stats, true) . "</pre>\n";
    
} catch (Exception $e) {
    echo "<p class='error'>✗ 缓存类测试失败: " . $e->getMessage() . "</p>\n";
}

// 测试4: 测试URL生成函数
echo "<h2>4. URL生成函数测试</h2>\n";
try {
    // 测试网站URL生成
    if (function_exists('get_website_url')) {
        $test_url = get_website_url(1, true);
        echo "<p class='success'>✓ 网站URL生成: {$test_url}</p>\n";
    } else {
        echo "<p class='error'>✗ get_website_url 函数不存在</p>\n";
    }
    
    // 测试文章URL生成
    if (function_exists('get_article_url')) {
        $test_url = get_article_url(1, true);
        echo "<p class='success'>✓ 文章URL生成: {$test_url}</p>\n";
    } else {
        echo "<p class='error'>✗ get_article_url 函数不存在</p>\n";
    }
    
    // 测试分类URL生成
    if (function_exists('get_category_url')) {
        $test_url = get_category_url('webdir', 1, 1, true);
        echo "<p class='success'>✓ 分类URL生成: {$test_url}</p>\n";
    } else {
        echo "<p class='error'>✗ get_category_url 函数不存在</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ URL生成函数测试失败: " . $e->getMessage() . "</p>\n";
}

// 测试5: 测试XML转义函数
echo "<h2>5. XML转义函数测试</h2>\n";
if (function_exists('xml_escape')) {
    $test_string = 'Test & "quotes" <tags>';
    $escaped = xml_escape($test_string);
    echo "<p class='success'>✓ XML转义函数存在</p>\n";
    echo "<p class='info'>原始: {$test_string}</p>\n";
    echo "<p class='info'>转义: {$escaped}</p>\n";
} else {
    echo "<p class='error'>✗ xml_escape 函数不存在</p>\n";
}

// 测试6: 模拟站点地图生成
echo "<h2>6. 站点地图生成测试</h2>\n";
echo "<p class='info'>测试站点地图URL:</p>\n";
$base_url = $options['site_url'];
$test_urls = array(
    '站点地图索引' => $base_url . '?mod=sitemap',
    '网站站点地图' => $base_url . '?mod=sitemap&type=websites&page=1',
    '文章站点地图' => $base_url . '?mod=sitemap&type=articles&page=1',
    '分类站点地图' => $base_url . '?mod=sitemap&type=categories',
    '页面站点地图' => $base_url . '?mod=sitemap&type=pages',
    '标签站点地图' => $base_url . '?mod=sitemap&type=tags'
);

foreach ($test_urls as $name => $url) {
    echo "<p class='info'>{$name}: <a href='{$url}' target='_blank'>{$url}</a></p>\n";
}

// 测试7: 检查必要的模块文件
echo "<h2>7. 模块文件检查</h2>\n";
$required_files = array(
    'module/sitemap.php' => '站点地图主模块',
    'source/module/sitemap_cache.php' => '缓存管理模块',
    'system/sitemap_manage.php' => '管理页面',
    'themes/system/sitemap_manage.html' => '管理模板'
);

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='success'>✓ {$description}: {$file}</p>\n";
    } else {
        echo "<p class='error'>✗ {$description}: {$file} (文件不存在)</p>\n";
    }
}

echo "<h2>测试完成</h2>\n";
echo "<p class='info'>如果所有测试都通过，您可以访问以下链接测试站点地图功能:</p>\n";
echo "<ul>\n";
echo "<li><a href='{$base_url}?mod=sitemap' target='_blank'>站点地图索引</a></li>\n";
echo "<li><a href='system/sitemap_manage.php' target='_blank'>站点地图管理页面</a></li>\n";
echo "</ul>\n";

?>
