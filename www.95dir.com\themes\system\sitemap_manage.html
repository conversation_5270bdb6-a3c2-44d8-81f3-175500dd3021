<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{#$pagetitle#} - 系统管理</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stats-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
        }
        .stats-card h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 16px;
        }
        .stats-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .stats-label {
            color: #6c757d;
            font-size: 14px;
        }
        .action-buttons {
            margin: 20px 0;
        }
        .action-buttons .btn {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .sitemap-links {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        .sitemap-links h3 {
            margin: 0 0 15px 0;
            color: #495057;
        }
        .sitemap-links ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .sitemap-links li {
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .sitemap-links li:last-child {
            border-bottom: none;
        }
        .sitemap-links a {
            color: #007bff;
            text-decoration: none;
        }
        .sitemap-links a:hover {
            text-decoration: underline;
        }
        .cache-info {
            background: #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    {#include file="header.html"#}
    
    <div class="container">
        <h2>{#$pagetitle#}</h2>
        
        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stats-card">
                <h3>网站数据</h3>
                <div class="stats-number">{#$stats.websites_count#}</div>
                <div class="stats-label">已审核网站</div>
                <div class="stats-label">分 {#$stats.websites_pages#} 个站点地图</div>
            </div>
            
            <div class="stats-card">
                <h3>文章数据</h3>
                <div class="stats-number">{#$stats.articles_count#}</div>
                <div class="stats-label">已发布文章</div>
                <div class="stats-label">分 {#$stats.articles_pages#} 个站点地图</div>
            </div>
            
            <div class="stats-card">
                <h3>分类数据</h3>
                <div class="stats-number">{#$stats.categories_count#}</div>
                <div class="stats-label">分类总数</div>
            </div>
            
            <div class="stats-card">
                <h3>缓存状态</h3>
                <div class="stats-number">{#$stats.cache_stats.valid_files#}</div>
                <div class="stats-label">有效缓存文件</div>
                <div class="cache-info">
                    总文件: {#$stats.cache_stats.total_files#}<br>
                    过期文件: {#$stats.cache_stats.expired_files#}<br>
                    缓存大小: {#$sitemap_cache->formatSize($stats.cache_stats.total_size)#}
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
            <a href="?action=view&type=index" class="btn btn-primary" target="_blank">查看站点地图索引</a>
            <a href="?action=generate&type=index" class="btn btn-success">重新生成索引</a>
            <a href="?action=clear_cache&type=all" class="btn btn-warning" onclick="return confirm('确定要清除所有缓存吗？')">清除所有缓存</a>
            <a href="?action=clear_expired" class="btn btn-info">清除过期缓存</a>
        </div>
        
        <!-- 站点地图链接 -->
        <div class="sitemap-links">
            <h3>站点地图链接</h3>
            <ul>
                <li>
                    <strong>主索引:</strong> 
                    <a href="{#$options.site_url#}?mod=sitemap" target="_blank">{#$options.site_url#}?mod=sitemap</a>
                    <a href="?action=generate&type=index" class="btn btn-sm btn-outline-secondary">重新生成</a>
                </li>
                
                {#for $i=1 to $stats.websites_pages#}
                <li>
                    <strong>网站站点地图 第{#$i#}页:</strong> 
                    <a href="{#$options.site_url#}?mod=sitemap&type=websites&page={#$i#}" target="_blank">查看</a>
                    <a href="?action=generate&type=websites&page={#$i#}" class="btn btn-sm btn-outline-secondary">重新生成</a>
                    <a href="?action=clear_cache&type=websites" class="btn btn-sm btn-outline-warning">清除缓存</a>
                </li>
                {#/for#}
                
                {#for $i=1 to $stats.articles_pages#}
                <li>
                    <strong>文章站点地图 第{#$i#}页:</strong> 
                    <a href="{#$options.site_url#}?mod=sitemap&type=articles&page={#$i#}" target="_blank">查看</a>
                    <a href="?action=generate&type=articles&page={#$i#}" class="btn btn-sm btn-outline-secondary">重新生成</a>
                    <a href="?action=clear_cache&type=articles" class="btn btn-sm btn-outline-warning">清除缓存</a>
                </li>
                {#/for#}
                
                <li>
                    <strong>分类站点地图:</strong> 
                    <a href="{#$options.site_url#}?mod=sitemap&type=categories" target="_blank">查看</a>
                    <a href="?action=generate&type=categories" class="btn btn-sm btn-outline-secondary">重新生成</a>
                    <a href="?action=clear_cache&type=categories" class="btn btn-sm btn-outline-warning">清除缓存</a>
                </li>
                
                <li>
                    <strong>页面站点地图:</strong> 
                    <a href="{#$options.site_url#}?mod=sitemap&type=pages" target="_blank">查看</a>
                    <a href="?action=generate&type=pages" class="btn btn-sm btn-outline-secondary">重新生成</a>
                    <a href="?action=clear_cache&type=pages" class="btn btn-sm btn-outline-warning">清除缓存</a>
                </li>
                
                <li>
                    <strong>标签站点地图:</strong> 
                    <a href="{#$options.site_url#}?mod=sitemap&type=tags" target="_blank">查看</a>
                    <a href="?action=generate&type=tags" class="btn btn-sm btn-outline-secondary">重新生成</a>
                    <a href="?action=clear_cache&type=tags" class="btn btn-sm btn-outline-warning">清除缓存</a>
                </li>
            </ul>
        </div>
        
        <!-- 使用说明 -->
        <div class="sitemap-links">
            <h3>使用说明</h3>
            <ul>
                <li><strong>站点地图索引:</strong> 包含所有子站点地图的链接，搜索引擎会首先访问这个文件</li>
                <li><strong>网站站点地图:</strong> 包含所有已审核网站的URL，按每50,000个URL分页</li>
                <li><strong>文章站点地图:</strong> 包含所有已发布文章的URL，按每50,000个URL分页</li>
                <li><strong>分类站点地图:</strong> 包含所有分类页面的URL</li>
                <li><strong>页面站点地图:</strong> 包含自定义页面和功能页面的URL</li>
                <li><strong>标签站点地图:</strong> 包含热门标签页面的URL</li>
                <li><strong>缓存机制:</strong> 站点地图会被缓存1小时，可以手动清除缓存强制重新生成</li>
            </ul>
        </div>
    </div>
    
    {#include file="footer.html"#}
</body>
</html>
