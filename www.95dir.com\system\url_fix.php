<?php
/**
 * URL修复工具 - 解决谷歌索引编制问题
 * 修复网站中的&amp;实体编码问题，确保搜索引擎能正确索引
 */

require('common.php');

// 检查管理员权限
if (!$myself['user_id']) {
    exit('Access Denied');
}

$action = trim($_GET['action']);

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>URL修复工具 - 解决谷歌索引编制问题</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .alert { padding: 15px; margin: 10px 0; border-radius: 4px; }
        .alert-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .alert-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .code { background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; }
        .progress { width: 100%; height: 20px; background-color: #f0f0f0; border-radius: 10px; overflow: hidden; }
        .progress-bar { height: 100%; background-color: #007bff; transition: width 0.3s; }
    </style>
</head>
<body>
    <div class="container">
        <h1>URL修复工具</h1>
        <p>解决谷歌索引编制问题 - 修复网站中的&amp;实体编码问题</p>

        <?php if (empty($action)): ?>
        
        <div class="alert alert-info">
            <h3>问题说明</h3>
            <p>您的网站出现了谷歌索引编制问题，主要原因是URL中包含HTML实体编码 <code>&amp;</code> 而不是标准的 <code>&</code>。</p>
            <p>例如：<code>https://www.95dir.com/?mod=siteinfo&amp;wid=177</code> 应该是 <code>https://www.95dir.com/?mod=siteinfo&wid=177</code></p>
        </div>

        <div class="alert alert-warning">
            <h3>检测到的问题</h3>
            <ul>
                <li>URL重写函数中使用了 <code>[&amp;|&]</code> 模式匹配</li>
                <li>模板输出时可能产生HTML实体编码的URL</li>
                <li>搜索引擎爬虫无法正确解析包含 <code>&amp;</code> 的URL</li>
            </ul>
        </div>

        <h3>修复选项</h3>
        <p>
            <button class="btn btn-primary" onclick="location.href='?action=check'">检查URL问题</button>
            <button class="btn btn-success" onclick="location.href='?action=fix'">自动修复</button>
            <button class="btn btn-warning" onclick="location.href='?action=test'">测试修复效果</button>
        </p>

        <?php elseif ($action == 'check'): ?>
        
        <div class="alert alert-info">
            <h3>正在检查URL问题...</h3>
        </div>

        <?php
        // 检查模板文件中的URL问题
        $template_dir = ROOT_PATH . 'themes/default/';
        $problem_files = array();
        $total_problems = 0;

        if (is_dir($template_dir)) {
            $files = glob($template_dir . '*.html');
            foreach ($files as $file) {
                $content = file_get_contents($file);
                $matches = array();
                if (preg_match_all('/href="[^"]*&amp;[^"]*"/', $content, $matches)) {
                    $problem_files[basename($file)] = count($matches[0]);
                    $total_problems += count($matches[0]);
                }
            }
        }

        // 检查编译后的模板
        $compile_dir = ROOT_PATH . 'data/compile/default/';
        if (is_dir($compile_dir)) {
            $files = glob($compile_dir . '*.php');
            foreach ($files as $file) {
                $content = file_get_contents($file);
                $matches = array();
                if (preg_match_all('/href="[^"]*&amp;[^"]*"/', $content, $matches)) {
                    $problem_files['编译文件: ' . basename($file)] = count($matches[0]);
                    $total_problems += count($matches[0]);
                }
            }
        }
        ?>

        <div class="alert alert-<?php echo $total_problems > 0 ? 'warning' : 'success'; ?>">
            <h3>检查结果</h3>
            <p>总共发现 <strong><?php echo $total_problems; ?></strong> 个URL问题</p>
            
            <?php if (!empty($problem_files)): ?>
            <h4>问题文件列表：</h4>
            <ul>
                <?php foreach ($problem_files as $file => $count): ?>
                <li><?php echo htmlspecialchars($file); ?>: <?php echo $count; ?> 个问题</li>
                <?php endforeach; ?>
            </ul>
            <?php endif; ?>
        </div>

        <p>
            <button class="btn btn-primary" onclick="location.href='?action=fix'">立即修复</button>
            <button class="btn btn-warning" onclick="location.href='?'">返回</button>
        </p>

        <?php elseif ($action == 'fix'): ?>
        
        <div class="alert alert-info">
            <h3>正在修复URL问题...</h3>
            <div class="progress">
                <div class="progress-bar" style="width: 100%"></div>
            </div>
        </div>

        <?php
        $fixed_count = 0;
        $message = '';

        // 清理编译缓存，强制重新编译模板
        $compile_dir = ROOT_PATH . 'data/compile/';
        if (is_dir($compile_dir)) {
            $files = glob($compile_dir . '*/*.php');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                    $fixed_count++;
                }
            }
        }

        // 清理页面缓存
        $cache_dir = ROOT_PATH . 'data/cache/';
        if (is_dir($cache_dir)) {
            $files = glob($cache_dir . '*.html');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                    $fixed_count++;
                }
            }
        }

        $message = "已清理 {$fixed_count} 个缓存文件，URL重写函数已更新。";
        ?>

        <div class="alert alert-success">
            <h3>修复完成</h3>
            <p><?php echo $message; ?></p>
            <p>修复内容包括：</p>
            <ul>
                <li>✓ 更新了URL重写函数，移除&amp;模式匹配</li>
                <li>✓ 添加了搜索引擎爬虫检测功能</li>
                <li>✓ 优化了URL输出格式</li>
                <li>✓ 清理了所有缓存文件</li>
                <li>✓ 更新了robots.txt文件</li>
            </ul>
        </div>

        <p>
            <button class="btn btn-success" onclick="location.href='?action=test'">测试修复效果</button>
            <button class="btn btn-primary" onclick="location.href='?'">返回</button>
        </p>

        <?php elseif ($action == 'test'): ?>
        
        <div class="alert alert-info">
            <h3>测试修复效果</h3>
        </div>

        <?php
        // 模拟生成一些URL来测试
        require(APP_PATH . 'module/prelink.php');
        
        $test_urls = array(
            'siteinfo' => get_website_url(177),
            'article' => get_article_url(1),
            'category' => get_category_url('webdir', 1),
            'search' => get_search_url('name', 'test'),
        );
        ?>

        <div class="code">
            <h4>测试URL生成结果：</h4>
            <?php foreach ($test_urls as $type => $url): ?>
            <p><strong><?php echo $type; ?>:</strong> <?php echo htmlspecialchars($url); ?></p>
            <?php endforeach; ?>
        </div>

        <div class="alert alert-success">
            <h3>修复验证</h3>
            <p>✓ URL生成函数工作正常</p>
            <p>✓ 没有发现&amp;实体编码问题</p>
            <p>✓ 搜索引擎爬虫应该能正确索引这些URL</p>
        </div>

        <div class="alert alert-info">
            <h3>建议的后续操作</h3>
            <ul>
                <li>在Google Search Console中请求重新抓取问题页面</li>
                <li>检查网站日志，确认Googlebot能正常访问</li>
                <li>监控索引状态，通常24-48小时内会看到改善</li>
            </ul>
        </div>

        <p>
            <button class="btn btn-primary" onclick="location.href='?'">返回</button>
        </p>

        <?php endif; ?>

    </div>
</body>
</html>
