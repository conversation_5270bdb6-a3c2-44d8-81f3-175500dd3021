<?php
// 站点地图详细诊断
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 初始化系统
define('IN_IWEBDIR', true);
define('APP_PATH', './');

// 包含系统文件
require_once('./source/init.php');

echo "<h1>站点地图详细诊断</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .warning{color:orange;}</style>";

// 测试1: 检查数据库连接和数据
echo "<h2>测试1: 数据库连接和数据检查</h2>";
try {
    if (isset($DB)) {
        echo "<p class='success'>✓ 数据库对象存在</p>";

        // 检查网站数据
        $websites_sql = "SELECT COUNT(*) as count FROM " . $DB->table('websites') . " WHERE web_status=3";
        $websites_result = $DB->query($websites_sql);
        $websites_row = $DB->fetch_array($websites_result);
        $websites_count = $websites_row['count'];

        echo "<p class='info'>已审核网站数量: {$websites_count}</p>";

        if ($websites_count > 0) {
            // 获取几个网站样本
            $sample_sql = "SELECT web_id, web_name, web_url FROM " . $DB->table('websites') . " WHERE web_status=3 LIMIT 5";
            $sample_result = $DB->query($sample_sql);
            echo "<p class='info'>网站样本:</p><ul>";
            while ($row = $DB->fetch_array($sample_result)) {
                echo "<li>ID: {$row['web_id']}, 名称: {$row['web_name']}, URL: {$row['web_url']}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p class='warning'>⚠ 没有已审核的网站数据</p>";
        }

        // 检查文章数据
        $articles_sql = "SELECT COUNT(*) as count FROM " . $DB->table('articles') . " WHERE art_status=3";
        $articles_result = $DB->query($articles_sql);
        $articles_row = $DB->fetch_array($articles_result);
        $articles_count = $articles_row['count'];

        echo "<p class='info'>已发布文章数量: {$articles_count}</p>";

    } else {
        echo "<p class='error'>✗ 数据库对象不存在</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ 数据库错误: " . $e->getMessage() . "</p>";
}

// 测试2: 检查配置
echo "<h2>测试2: 系统配置检查</h2>";
if (isset($options)) {
    echo "<p class='success'>✓ 配置选项存在</p>";
    echo "<p class='info'>站点URL: " . $options['site_url'] . "</p>";
} else {
    echo "<p class='error'>✗ 配置选项不存在</p>";
}

// 测试3: 测试URL生成函数
echo "<h2>测试3: URL生成函数检查</h2>";
if (function_exists('get_website_url')) {
    echo "<p class='success'>✓ get_website_url 函数存在</p>";

    // 测试生成URL
    if ($websites_count > 0) {
        try {
            $test_url = get_website_url(1, true);
            echo "<p class='info'>测试网站URL: {$test_url}</p>";
        } catch (Exception $e) {
            echo "<p class='error'>URL生成错误: " . $e->getMessage() . "</p>";
        }
    }
} else {
    echo "<p class='warning'>⚠ get_website_url 函数不存在</p>";

    // 尝试包含prelink模块
    if (file_exists('./source/module/prelink.php')) {
        require_once('./source/module/prelink.php');
        if (function_exists('get_website_url')) {
            echo "<p class='success'>✓ 成功加载 get_website_url 函数</p>";
        } else {
            echo "<p class='error'>✗ 加载prelink模块后仍无法找到函数</p>";
        }
    } else {
        echo "<p class='error'>✗ prelink.php 文件不存在</p>";
    }
}

if (function_exists('get_article_url')) {
    echo "<p class='success'>✓ get_article_url 函数存在</p>";
} else {
    echo "<p class='warning'>⚠ get_article_url 函数不存在</p>";
}

// 测试4: 手动生成简单站点地图
echo "<h2>测试4: 手动生成站点地图测试</h2>";
try {
    echo "<p class='info'>尝试手动生成站点地图...</p>";

    // 开始生成XML
    ob_start();

    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

    // 添加首页
    echo "    <url>\n";
    echo "        <loc>" . htmlspecialchars($options['site_url'], ENT_QUOTES, 'UTF-8') . "</loc>\n";
    echo "        <lastmod>" . date('c') . "</lastmod>\n";
    echo "        <changefreq>daily</changefreq>\n";
    echo "        <priority>1.0</priority>\n";
    echo "    </url>\n";

    // 添加网站链接
    if ($websites_count > 0) {
        $sql = "SELECT web_id, web_name, web_url, web_ctime FROM " . $DB->table('websites') . " WHERE web_status=3 ORDER BY web_id DESC LIMIT 10";
        $query = $DB->query($sql);
        $added_count = 0;

        while ($row = $DB->fetch_array($query)) {
            // 生成URL
            if (function_exists('get_website_url')) {
                $web_url = get_website_url($row['web_id'], true, $row['web_name'], $row['web_url']);
            } else {
                $web_url = $options['site_url'] . '?mod=siteinfo&wid=' . $row['web_id'];
            }

            echo "    <url>\n";
            echo "        <loc>" . htmlspecialchars($web_url, ENT_QUOTES, 'UTF-8') . "</loc>\n";
            echo "        <lastmod>" . date('c', $row['web_ctime']) . "</lastmod>\n";
            echo "        <changefreq>weekly</changefreq>\n";
            echo "        <priority>0.6</priority>\n";
            echo "    </url>\n";

            $added_count++;
        }
        $DB->free_result($query);

        echo "<p class='success'>✓ 成功添加 {$added_count} 个网站链接</p>";
    }

    echo '</urlset>' . "\n";

    $xml_content = ob_get_contents();
    ob_end_clean();

    echo "<p class='success'>✓ 站点地图生成成功！</p>";
    echo "<p><a href='#' onclick='showXML()'>查看生成的XML</a></p>";
    echo "<div id='xml-content' style='display:none; background:#f5f5f5; padding:10px; margin:10px 0; border-radius:4px; max-height:300px; overflow:auto;'>";
    echo "<pre>" . htmlspecialchars($xml_content) . "</pre>";
    echo "</div>";

} catch (Exception $e) {
    echo "<p class='error'>✗ 生成站点地图时出错: " . $e->getMessage() . "</p>";
}

// 测试5: 检查站点地图模块
echo "<h2>测试5: 站点地图模块检查</h2>";
if (file_exists('./module/sitemap.php')) {
    echo "<p class='success'>✓ sitemap.php 文件存在</p>";

    // 检查文件内容
    $content = file_get_contents('./module/sitemap.php');
    if (strpos($content, 'generate_simple_sitemap_index') !== false) {
        echo "<p class='success'>✓ 包含简化函数</p>";
    } else {
        echo "<p class='warning'>⚠ 可能缺少简化函数</p>";
    }

    if (strpos($content, 'sitemap_error') !== false) {
        echo "<p class='success'>✓ 包含错误处理函数</p>";
    } else {
        echo "<p class='warning'>⚠ 可能缺少错误处理</p>";
    }
} else {
    echo "<p class='error'>✗ sitemap.php 文件不存在</p>";
}

echo "<script>
function showXML() {
    var div = document.getElementById('xml-content');
    div.style.display = div.style.display === 'none' ? 'block' : 'none';
}
</script>";

echo "<h2>测试完成</h2>";
echo "<p class='info'>现在可以尝试访问: <a href='?mod=sitemap' target='_blank'>?mod=sitemap</a></p>";
?>

// 测试3: 检查PHP语法
echo "<h2>测试3: 检查PHP语法</h2>";
$output = array();
$return_var = 0;
exec('php -l ./module/sitemap.php 2>&1', $output, $return_var);

if ($return_var === 0) {
    echo "<p style='color:green'>✓ PHP语法检查通过</p>";
} else {
    echo "<p style='color:red'>✗ PHP语法错误:</p>";
    echo "<pre>" . implode("\n", $output) . "</pre>";
}

// 测试4: 模拟包含文件
echo "<h2>测试4: 模拟包含站点地图文件</h2>";
try {
    // 设置必要的常量和变量
    if (!defined('IN_IWEBDIR')) {
        define('IN_IWEBDIR', true);
    }
    
    // 模拟全局变量
    $GLOBALS['DB'] = new stdClass();
    $GLOBALS['options'] = array('site_url' => 'https://www.95dir.com/');
    
    // 尝试包含文件
    ob_start();
    $_GET['type'] = 'index'; // 设置测试参数
    
    // 这里不实际包含，只是检查
    echo "<p style='color:blue'>准备包含站点地图文件...</p>";
    echo "<p>如果上面的语法检查通过，文件应该可以正常包含</p>";
    
    ob_end_clean();
    
} catch (Exception $e) {
    echo "<p style='color:red'>包含文件时出错: " . $e->getMessage() . "</p>";
}

// 测试5: 提供替代方案
echo "<h2>测试5: 替代方案</h2>";
echo "<p>如果主站点地图不工作，可以使用以下替代方案:</p>";
echo "<ul>";
echo "<li><a href='sitemap_minimal.php' target='_blank'>极简站点地图</a></li>";
echo "<li><a href='sitemap_test_page.html' target='_blank'>站点地图测试页面</a></li>";
echo "</ul>";

// 测试6: 服务器信息
echo "<h2>测试6: 服务器信息</h2>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>服务器软件: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";

?>
