<?php
// 最简单的站点地图测试
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>站点地图测试</h1>";

// 测试1: 直接访问站点地图模块
echo "<h2>测试1: 直接访问站点地图</h2>";
echo "<p><a href='?mod=sitemap' target='_blank'>点击测试站点地图</a></p>";

// 测试2: 检查模块文件
echo "<h2>测试2: 检查模块文件</h2>";
if (file_exists('./module/sitemap.php')) {
    echo "<p style='color:green'>✓ sitemap.php 文件存在</p>";
    
    // 检查文件大小
    $filesize = filesize('./module/sitemap.php');
    echo "<p>文件大小: " . number_format($filesize) . " 字节</p>";
    
    // 检查文件权限
    if (is_readable('./module/sitemap.php')) {
        echo "<p style='color:green'>✓ 文件可读</p>";
    } else {
        echo "<p style='color:red'>✗ 文件不可读</p>";
    }
} else {
    echo "<p style='color:red'>✗ sitemap.php 文件不存在</p>";
}

// 测试3: 检查PHP语法
echo "<h2>测试3: 检查PHP语法</h2>";
$output = array();
$return_var = 0;
exec('php -l ./module/sitemap.php 2>&1', $output, $return_var);

if ($return_var === 0) {
    echo "<p style='color:green'>✓ PHP语法检查通过</p>";
} else {
    echo "<p style='color:red'>✗ PHP语法错误:</p>";
    echo "<pre>" . implode("\n", $output) . "</pre>";
}

// 测试4: 模拟包含文件
echo "<h2>测试4: 模拟包含站点地图文件</h2>";
try {
    // 设置必要的常量和变量
    if (!defined('IN_IWEBDIR')) {
        define('IN_IWEBDIR', true);
    }
    
    // 模拟全局变量
    $GLOBALS['DB'] = new stdClass();
    $GLOBALS['options'] = array('site_url' => 'https://www.95dir.com/');
    
    // 尝试包含文件
    ob_start();
    $_GET['type'] = 'index'; // 设置测试参数
    
    // 这里不实际包含，只是检查
    echo "<p style='color:blue'>准备包含站点地图文件...</p>";
    echo "<p>如果上面的语法检查通过，文件应该可以正常包含</p>";
    
    ob_end_clean();
    
} catch (Exception $e) {
    echo "<p style='color:red'>包含文件时出错: " . $e->getMessage() . "</p>";
}

// 测试5: 提供替代方案
echo "<h2>测试5: 替代方案</h2>";
echo "<p>如果主站点地图不工作，可以使用以下替代方案:</p>";
echo "<ul>";
echo "<li><a href='sitemap_minimal.php' target='_blank'>极简站点地图</a></li>";
echo "<li><a href='sitemap_test_page.html' target='_blank'>站点地图测试页面</a></li>";
echo "</ul>";

// 测试6: 服务器信息
echo "<h2>测试6: 服务器信息</h2>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>服务器软件: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";

?>
