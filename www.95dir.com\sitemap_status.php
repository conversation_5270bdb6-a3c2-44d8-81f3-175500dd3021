<?php
/**
 * 站点地图状态检查页面
 * 快速检查站点地图功能是否正常工作
 */
define('IN_IWEBDIR', true);
define('APP_PATH', './');

require_once('./source/init.php');

// 检查是否有管理员权限（简单检查）
$is_admin = isset($_GET['admin']) && $_GET['admin'] == 'true';

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>站点地图状态检查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .status-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; }
        .status-ok { border-left: 4px solid #28a745; background: #f8fff9; }
        .status-warning { border-left: 4px solid #ffc107; background: #fffdf5; }
        .status-error { border-left: 4px solid #dc3545; background: #fff5f5; }
        .status-title { font-weight: bold; margin-bottom: 10px; }
        .status-detail { font-size: 14px; color: #666; margin: 5px 0; }
        .btn { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        h1 { color: #333; text-align: center; }
        h2 { color: #555; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .links-section { margin-top: 30px; }
        .links-section ul { list-style: none; padding: 0; }
        .links-section li { padding: 8px 0; border-bottom: 1px solid #eee; }
        .links-section a { color: #007bff; text-decoration: none; }
        .links-section a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h1>95分类目录 - 站点地图状态检查</h1>
        
        <div class="status-grid">
            <?php
            // 检查1: 核心文件
            $core_files = array(
                'module/sitemap.php' => '站点地图主模块',
                'source/module/sitemap_cache.php' => '缓存管理模块',
                'source/module/prelink.php' => 'URL生成模块',
                'source/module/category.php' => '分类模块'
            );
            
            $files_ok = true;
            foreach ($core_files as $file => $desc) {
                if (!file_exists($file)) {
                    $files_ok = false;
                    break;
                }
            }
            ?>
            
            <div class="status-card <?php echo $files_ok ? 'status-ok' : 'status-error'; ?>">
                <div class="status-title">核心文件检查</div>
                <?php if ($files_ok): ?>
                    <div class="status-detail">✓ 所有核心文件存在</div>
                <?php else: ?>
                    <div class="status-detail">✗ 缺少核心文件</div>
                    <?php foreach ($core_files as $file => $desc): ?>
                        <?php if (!file_exists($file)): ?>
                            <div class="status-detail">缺少: <?php echo $desc; ?> (<?php echo $file; ?>)</div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            
            <?php
            // 检查2: 缓存目录
            $cache_dir = './data/cache/sitemap/';
            $cache_ok = is_dir($cache_dir) && is_writable($cache_dir);
            ?>
            
            <div class="status-card <?php echo $cache_ok ? 'status-ok' : 'status-warning'; ?>">
                <div class="status-title">缓存目录检查</div>
                <?php if ($cache_ok): ?>
                    <div class="status-detail">✓ 缓存目录可用</div>
                    <div class="status-detail">路径: <?php echo realpath($cache_dir); ?></div>
                    <?php
                    $cache_files = glob($cache_dir . '*.xml');
                    echo '<div class="status-detail">缓存文件数: ' . count($cache_files) . '</div>';
                    ?>
                <?php else: ?>
                    <div class="status-detail">⚠ 缓存目录问题</div>
                    <?php if (!is_dir($cache_dir)): ?>
                        <div class="status-detail">目录不存在: <?php echo $cache_dir; ?></div>
                    <?php elseif (!is_writable($cache_dir)): ?>
                        <div class="status-detail">目录不可写: <?php echo $cache_dir; ?></div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            
            <?php
            // 检查3: 数据库连接
            $db_ok = false;
            $websites_count = 0;
            $articles_count = 0;
            try {
                $websites_count = $DB->get_count($DB->table('websites'), "web_status=3");
                $articles_count = $DB->get_count($DB->table('articles'), "art_status=3");
                $db_ok = true;
            } catch (Exception $e) {
                $db_error = $e->getMessage();
            }
            ?>
            
            <div class="status-card <?php echo $db_ok ? 'status-ok' : 'status-error'; ?>">
                <div class="status-title">数据库检查</div>
                <?php if ($db_ok): ?>
                    <div class="status-detail">✓ 数据库连接正常</div>
                    <div class="status-detail">已审核网站: <?php echo $websites_count; ?></div>
                    <div class="status-detail">已发布文章: <?php echo $articles_count; ?></div>
                <?php else: ?>
                    <div class="status-detail">✗ 数据库连接失败</div>
                    <div class="status-detail"><?php echo isset($db_error) ? $db_error : '未知错误'; ?></div>
                <?php endif; ?>
            </div>
            
            <?php
            // 检查4: URL重写规则
            $htaccess_ok = file_exists('.htaccess');
            $has_sitemap_rules = false;
            if ($htaccess_ok) {
                $htaccess_content = file_get_contents('.htaccess');
                $has_sitemap_rules = strpos($htaccess_content, 'sitemap') !== false;
            }
            ?>
            
            <div class="status-card <?php echo ($htaccess_ok && $has_sitemap_rules) ? 'status-ok' : 'status-warning'; ?>">
                <div class="status-title">URL重写检查</div>
                <?php if ($htaccess_ok): ?>
                    <div class="status-detail">✓ .htaccess文件存在</div>
                    <?php if ($has_sitemap_rules): ?>
                        <div class="status-detail">✓ 包含站点地图规则</div>
                    <?php else: ?>
                        <div class="status-detail">⚠ 可能缺少站点地图规则</div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="status-detail">⚠ .htaccess文件不存在</div>
                <?php endif; ?>
            </div>
        </div>
        
        <h2>快速操作</h2>
        <div style="text-align: center; margin: 20px 0;">
            <a href="<?php echo $options['site_url']; ?>?mod=sitemap" class="btn btn-success" target="_blank">查看站点地图索引</a>
            <a href="<?php echo $options['site_url']; ?>?mod=sitemap&type=websites" class="btn" target="_blank">网站站点地图</a>
            <a href="<?php echo $options['site_url']; ?>?mod=sitemap&type=articles" class="btn" target="_blank">文章站点地图</a>
            <?php if ($is_admin): ?>
                <a href="system/sitemap_manage.php" class="btn btn-warning" target="_blank">管理页面</a>
            <?php endif; ?>
        </div>
        
        <div class="links-section">
            <h2>站点地图链接</h2>
            <ul>
                <li><strong>主索引:</strong> <a href="<?php echo $options['site_url']; ?>?mod=sitemap" target="_blank"><?php echo $options['site_url']; ?>?mod=sitemap</a></li>
                <li><strong>网站站点地图:</strong> <a href="<?php echo $options['site_url']; ?>?mod=sitemap&type=websites" target="_blank"><?php echo $options['site_url']; ?>?mod=sitemap&type=websites</a></li>
                <li><strong>文章站点地图:</strong> <a href="<?php echo $options['site_url']; ?>?mod=sitemap&type=articles" target="_blank"><?php echo $options['site_url']; ?>?mod=sitemap&type=articles</a></li>
                <li><strong>分类站点地图:</strong> <a href="<?php echo $options['site_url']; ?>?mod=sitemap&type=categories" target="_blank"><?php echo $options['site_url']; ?>?mod=sitemap&type=categories</a></li>
                <li><strong>页面站点地图:</strong> <a href="<?php echo $options['site_url']; ?>?mod=sitemap&type=pages" target="_blank"><?php echo $options['site_url']; ?>?mod=sitemap&type=pages</a></li>
                <li><strong>标签站点地图:</strong> <a href="<?php echo $options['site_url']; ?>?mod=sitemap&type=tags" target="_blank"><?php echo $options['site_url']; ?>?mod=sitemap&type=tags</a></li>
            </ul>
        </div>
        
        <div class="links-section">
            <h2>SEO友好URL（需要URL重写支持）</h2>
            <ul>
                <li><strong>主索引:</strong> <a href="<?php echo $options['site_url']; ?>sitemap.xml" target="_blank"><?php echo $options['site_url']; ?>sitemap.xml</a></li>
                <li><strong>网站站点地图:</strong> <a href="<?php echo $options['site_url']; ?>sitemap-websites.xml" target="_blank"><?php echo $options['site_url']; ?>sitemap-websites.xml</a></li>
                <li><strong>文章站点地图:</strong> <a href="<?php echo $options['site_url']; ?>sitemap-articles.xml" target="_blank"><?php echo $options['site_url']; ?>sitemap-articles.xml</a></li>
            </ul>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 4px; font-size: 14px; color: #666;">
            <strong>说明:</strong> 
            <ul>
                <li>绿色表示功能正常</li>
                <li>黄色表示有警告但不影响基本功能</li>
                <li>红色表示有错误需要修复</li>
                <li>建议定期检查站点地图状态以确保搜索引擎能正常抓取</li>
            </ul>
        </div>
    </div>
</body>
</html>
