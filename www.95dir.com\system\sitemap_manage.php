<?php
/**
 * 站点地图管理页面
 * 提供站点地图的查看、缓存管理等功能
 */
require('common.php');
require(APP_PATH.'source/module/sitemap_cache.php');

$fileurl = 'sitemap_manage.php';
$tempfile = 'sitemap_manage.html';

if (!isset($action)) $action = 'index';

// 获取站点地图缓存实例
$sitemap_cache = get_sitemap_cache();

/** 主页面 */
if ($action == 'index') {
    $pagetitle = '站点地图管理';
    
    // 获取统计信息
    $stats = array(
        'websites_count' => get_websites_count(),
        'articles_count' => get_articles_count(),
        'categories_count' => get_categories_count(),
        'cache_stats' => $sitemap_cache->getStats()
    );
    
    // 计算站点地图分页信息
    $max_urls_per_sitemap = 50000;
    $stats['websites_pages'] = ceil($stats['websites_count'] / $max_urls_per_sitemap);
    $stats['articles_pages'] = ceil($stats['articles_count'] / $max_urls_per_sitemap);
    
    $smarty->assign('stats', $stats);
    $smarty->assign('h_action', 'index');
}

/** 清除缓存 */
if ($action == 'clear_cache') {
    $type = trim($_GET['type'] ?? 'all');
    
    if ($type == 'all') {
        $result = $sitemap_cache->clear();
        $message = $result ? '所有站点地图缓存已清除' : '清除缓存失败';
    } else {
        $result = clear_sitemap_cache($type);
        $message = $result ? "已清除 {$type} 类型的站点地图缓存" : '清除缓存失败';
    }
    
    msgbox($message, '?action=index');
}

/** 清除过期缓存 */
if ($action == 'clear_expired') {
    $result = $sitemap_cache->clearExpired();
    $message = $result ? '过期缓存已清除' : '清除过期缓存失败';
    msgbox($message, '?action=index');
}

/** 生成站点地图 */
if ($action == 'generate') {
    $type = trim($_GET['type'] ?? 'index');
    $page = intval($_GET['page'] ?? 1);
    
    // 清除对应的缓存
    if ($type == 'index') {
        $sitemap_cache->delete('sitemap_index');
    } else {
        $cache_key = $sitemap_cache->generateKey('sitemap_' . $type, array('page' => $page));
        $sitemap_cache->delete($cache_key);
    }
    
    // 重定向到站点地图URL以生成新的缓存
    $sitemap_url = $options['site_url'] . '?mod=sitemap';
    if ($type != 'index') {
        $sitemap_url .= '&type=' . $type;
        if ($page > 1) {
            $sitemap_url .= '&page=' . $page;
        }
    }
    
    redirect($sitemap_url);
}

/** 查看站点地图 */
if ($action == 'view') {
    $type = trim($_GET['type'] ?? 'index');
    $page = intval($_GET['page'] ?? 1);
    
    $sitemap_url = $options['site_url'] . '?mod=sitemap';
    if ($type != 'index') {
        $sitemap_url .= '&type=' . $type;
        if ($page > 1) {
            $sitemap_url .= '&page=' . $page;
        }
    }
    
    redirect($sitemap_url);
}

/** 获取网站总数 */
function get_websites_count() {
    global $DB;
    
    $where = "web_status=3";
    
    // 检查违规状态字段是否存在
    $table_name = $DB->table('websites');
    $check_sql = "SHOW COLUMNS FROM `{$table_name}` LIKE 'web_violation_status'";
    $check_result = $DB->query($check_sql);
    $has_violation_field = $DB->num_rows($check_result) > 0;
    
    if ($has_violation_field) {
        $where .= " AND (web_violation_status IS NULL OR web_violation_status=0)";
    }
    
    return $DB->get_count($DB->table('websites'), $where);
}

/** 获取文章总数 */
function get_articles_count() {
    global $DB;
    
    $where = "art_status=3";
    return $DB->get_count($DB->table('articles'), $where);
}

/** 获取分类总数 */
function get_categories_count() {
    global $DB;
    
    return $DB->get_count($DB->table('categories'), '1');
}

smarty_output($tempfile);
?>
