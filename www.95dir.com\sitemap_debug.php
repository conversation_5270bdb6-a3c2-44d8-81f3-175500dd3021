<?php
/**
 * 站点地图调试页面
 * 用于诊断站点地图白屏问题
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>站点地图调试信息</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .error{color:red;} .success{color:green;} .info{color:blue;}</style>";

// 检查1: PHP版本
echo "<h2>1. PHP版本检查</h2>";
echo "<p class='info'>PHP版本: " . PHP_VERSION . "</p>";

if (version_compare(PHP_VERSION, '5.6.0', '>=')) {
    echo "<p class='success'>✓ PHP版本支持</p>";
} else {
    echo "<p class='error'>✗ PHP版本过低，建议升级到5.6+</p>";
}

// 检查2: 常量定义
echo "<h2>2. 常量检查</h2>";
if (!defined('IN_IWEBDIR')) {
    define('IN_IWEBDIR', true);
    echo "<p class='info'>定义 IN_IWEBDIR 常量</p>";
}

if (!defined('APP_PATH')) {
    define('APP_PATH', './');
    echo "<p class='info'>定义 APP_PATH 常量</p>";
}

// 检查3: 核心文件
echo "<h2>3. 核心文件检查</h2>";
$core_files = array(
    './source/init.php' => '初始化文件',
    './source/include/function.php' => '函数库',
    './source/module/prelink.php' => 'URL生成模块',
    './source/module/category.php' => '分类模块'
);

foreach ($core_files as $file => $desc) {
    if (file_exists($file)) {
        echo "<p class='success'>✓ {$desc}: {$file}</p>";
    } else {
        echo "<p class='error'>✗ {$desc}: {$file} (文件不存在)</p>";
    }
}

// 检查4: 尝试包含核心文件
echo "<h2>4. 包含核心文件</h2>";
try {
    if (file_exists('./source/init.php')) {
        require_once('./source/init.php');
        echo "<p class='success'>✓ 成功包含 init.php</p>";
        
        // 检查数据库连接
        if (isset($DB)) {
            echo "<p class='success'>✓ 数据库对象存在</p>";
        } else {
            echo "<p class='error'>✗ 数据库对象不存在</p>";
        }
        
        // 检查选项
        if (isset($options)) {
            echo "<p class='success'>✓ 选项数组存在</p>";
            echo "<p class='info'>站点URL: " . (isset($options['site_url']) ? $options['site_url'] : '未设置') . "</p>";
        } else {
            echo "<p class='error'>✗ 选项数组不存在</p>";
        }
        
    } else {
        echo "<p class='error'>✗ init.php 文件不存在</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ 包含 init.php 时出错: " . $e->getMessage() . "</p>";
}

// 检查5: 尝试包含URL生成模块
echo "<h2>5. URL生成模块检查</h2>";
try {
    if (file_exists('./source/module/prelink.php')) {
        require_once('./source/module/prelink.php');
        echo "<p class='success'>✓ 成功包含 prelink.php</p>";
        
        if (function_exists('get_website_url')) {
            echo "<p class='success'>✓ get_website_url 函数存在</p>";
        } else {
            echo "<p class='error'>✗ get_website_url 函数不存在</p>";
        }
        
        if (function_exists('get_article_url')) {
            echo "<p class='success'>✓ get_article_url 函数存在</p>";
        } else {
            echo "<p class='error'>✗ get_article_url 函数不存在</p>";
        }
        
    } else {
        echo "<p class='error'>✗ prelink.php 文件不存在</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ 包含 prelink.php 时出错: " . $e->getMessage() . "</p>";
}

// 检查6: 测试简单的站点地图生成
echo "<h2>6. 简单站点地图测试</h2>";
try {
    if (isset($options) && isset($options['site_url'])) {
        echo "<p class='info'>尝试生成简单的站点地图...</p>";
        
        // 生成最基本的站点地图
        $xml_content = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml_content .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        $xml_content .= '    <url>' . "\n";
        $xml_content .= '        <loc>' . htmlspecialchars($options['site_url'], ENT_QUOTES, 'UTF-8') . '</loc>' . "\n";
        $xml_content .= '        <lastmod>' . date('c') . '</lastmod>' . "\n";
        $xml_content .= '        <changefreq>daily</changefreq>' . "\n";
        $xml_content .= '        <priority>1.0</priority>' . "\n";
        $xml_content .= '    </url>' . "\n";
        $xml_content .= '</urlset>' . "\n";
        
        echo "<p class='success'>✓ 基本站点地图生成成功</p>";
        echo "<p><a href='#' onclick='showXML()'>查看生成的XML</a></p>";
        echo "<div id='xml-content' style='display:none; background:#f5f5f5; padding:10px; margin:10px 0; border-radius:4px;'>";
        echo "<pre>" . htmlspecialchars($xml_content) . "</pre>";
        echo "</div>";
        
    } else {
        echo "<p class='error'>✗ 无法获取站点URL配置</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ 生成站点地图时出错: " . $e->getMessage() . "</p>";
}

// 检查7: 测试访问站点地图URL
echo "<h2>7. 站点地图URL测试</h2>";
if (isset($options) && isset($options['site_url'])) {
    $sitemap_urls = array(
        '站点地图索引' => $options['site_url'] . '?mod=sitemap',
        '网站站点地图' => $options['site_url'] . '?mod=sitemap&type=websites',
        '简单测试' => $options['site_url'] . 'sitemap_simple_test.php'
    );
    
    foreach ($sitemap_urls as $name => $url) {
        echo "<p class='info'>{$name}: <a href='{$url}' target='_blank'>{$url}</a></p>";
    }
}

echo "<script>
function showXML() {
    var div = document.getElementById('xml-content');
    div.style.display = div.style.display === 'none' ? 'block' : 'none';
}
</script>";

echo "<h2>调试完成</h2>";
echo "<p class='info'>如果上述检查都通过，但站点地图仍然白屏，请检查服务器错误日志。</p>";
?>
