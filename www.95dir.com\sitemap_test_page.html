<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>站点地图测试页面</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        h1 { 
            color: #333; 
            text-align: center; 
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        .test-section h3 { 
            margin-top: 0; 
            color: #555; 
        }
        .btn { 
            display: inline-block; 
            padding: 8px 16px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px; 
            margin: 5px; 
        }
        .btn:hover { 
            background: #0056b3; 
        }
        .btn-success { 
            background: #28a745; 
        }
        .btn-warning { 
            background: #ffc107; 
            color: #212529; 
        }
        .btn-danger { 
            background: #dc3545; 
        }
        .status { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px; 
        }
        .status-info { 
            background: #d1ecf1; 
            border: 1px solid #bee5eb; 
            color: #0c5460; 
        }
        .status-warning { 
            background: #fff3cd; 
            border: 1px solid #ffeaa7; 
            color: #856404; 
        }
        ul { 
            list-style-type: none; 
            padding: 0; 
        }
        li { 
            padding: 8px 0; 
            border-bottom: 1px solid #eee; 
        }
        li:last-child { 
            border-bottom: none; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>95分类目录 - 站点地图测试</h1>
        
        <div class="status status-info">
            <strong>说明:</strong> 如果原始站点地图出现白屏，请使用下面的测试链接来诊断问题。
        </div>
        
        <div class="test-section">
            <h3>1. 基础测试</h3>
            <p>首先测试PHP基础功能是否正常：</p>
            <a href="basic_test.php" class="btn btn-success" target="_blank">PHP基础测试</a>
        </div>
        
        <div class="test-section">
            <h3>2. 极简站点地图（推荐）</h3>
            <p>不依赖框架的简单站点地图，最稳定：</p>
            <ul>
                <li><a href="sitemap_minimal.php" class="btn" target="_blank">站点地图索引</a></li>
                <li><a href="sitemap_minimal.php?type=websites" class="btn" target="_blank">网站站点地图</a></li>
                <li><a href="sitemap_minimal.php?type=articles" class="btn" target="_blank">文章站点地图</a></li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>3. 原始站点地图</h3>
            <p>使用系统框架的完整站点地图：</p>
            <ul>
                <li><a href="?mod=sitemap" class="btn btn-warning" target="_blank">站点地图索引</a></li>
                <li><a href="?mod=sitemap&type=websites" class="btn btn-warning" target="_blank">网站站点地图</a></li>
                <li><a href="?mod=sitemap&type=articles" class="btn btn-warning" target="_blank">文章站点地图</a></li>
                <li><a href="?mod=sitemap&debug=1" class="btn btn-danger" target="_blank">调试模式</a></li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>4. 搜索引擎提交</h3>
            <p>将以下URL提交给搜索引擎：</p>
            <div class="status status-warning">
                <strong>推荐使用极简版本:</strong><br>
                <code>https://www.95dir.com/sitemap_minimal.php</code>
            </div>
            <p>或者使用原始版本（如果正常工作）：</p>
            <div class="status status-info">
                <code>https://www.95dir.com/?mod=sitemap</code>
            </div>
        </div>
        
        <div class="test-section">
            <h3>5. robots.txt 配置</h3>
            <p>在 robots.txt 文件中添加以下行：</p>
            <div class="status status-info">
                <code>Sitemap: https://www.95dir.com/sitemap_minimal.php</code>
            </div>
        </div>
        
        <div class="test-section">
            <h3>6. 故障排除</h3>
            <p>如果站点地图仍然不工作，请检查：</p>
            <ul>
                <li>PHP版本是否支持（建议PHP 5.6+）</li>
                <li>数据库连接是否正常</li>
                <li>文件权限是否正确</li>
                <li>服务器错误日志</li>
            </ul>
        </div>
        
        <div class="status status-info">
            <strong>技术支持:</strong> 如果问题持续存在，建议使用极简版本 sitemap_minimal.php，它不依赖复杂的框架，更加稳定可靠。
        </div>
    </div>
</body>
</html>
