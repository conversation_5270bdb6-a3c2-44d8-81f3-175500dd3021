<?php
/**
 * URL修复测试页面
 * 用于验证谷歌索引编制问题的修复效果
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');
require(APP_PATH.'module/prelink.php');
require(APP_PATH.'module/rewrite.php');

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>URL修复测试 - 95分类目录</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .code { background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        th { background-color: #f8f9fa; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>URL修复测试页面</h1>
        <p>测试谷歌索引编制问题的修复效果</p>

        <!-- 用户代理检测 -->
        <div class="test-section">
            <h2>1. 用户代理检测</h2>
            <?php
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
            $is_bot = is_search_engine_bot();
            ?>
            <p><strong>当前用户代理:</strong> <?php echo htmlspecialchars($user_agent); ?></p>
            <p><strong>是否为搜索引擎爬虫:</strong> 
                <span class="<?php echo $is_bot ? 'status-ok' : 'status-error'; ?>">
                    <?php echo $is_bot ? '是' : '否'; ?>
                </span>
            </p>
        </div>

        <!-- URL生成测试 -->
        <div class="test-section">
            <h2>2. URL生成测试</h2>
            <table>
                <thead>
                    <tr>
                        <th>URL类型</th>
                        <th>生成的URL</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $test_cases = array(
                        'siteinfo' => get_website_url(177),
                        'article' => get_article_url(1),
                        'category_webdir' => get_category_url('webdir', 1),
                        'category_article' => get_category_url('article', 1),
                        'search' => get_search_url('name', 'test'),
                        'update' => get_update_url(7),
                        'archives' => get_archives_url(202508),
                    );

                    foreach ($test_cases as $type => $url):
                        $has_amp = strpos($url, '&amp;') !== false;
                        $status_class = $has_amp ? 'status-error' : 'status-ok';
                        $status_text = $has_amp ? '包含&amp;' : '正常';
                    ?>
                    <tr>
                        <td><?php echo htmlspecialchars($type); ?></td>
                        <td><code><?php echo htmlspecialchars($url); ?></code></td>
                        <td><span class="<?php echo $status_class; ?>"><?php echo $status_text; ?></span></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- 重写函数测试 -->
        <div class="test-section">
            <h2>3. URL重写函数测试</h2>
            <?php
            // 测试包含&amp;的HTML内容
            $test_html = '
                <a href="?mod=siteinfo&amp;wid=177">测试链接1</a>
                <a href="?mod=webdir&amp;cid=1&amp;page=2">测试链接2</a>
                <a href="?mod=search&amp;type=name&amp;query=test">测试链接3</a>
            ';
            
            $rewritten_html = rewrite_output($test_html);
            ?>
            
            <h3>原始HTML:</h3>
            <div class="code"><?php echo htmlspecialchars($test_html); ?></div>
            
            <h3>重写后HTML:</h3>
            <div class="code"><?php echo htmlspecialchars($rewritten_html); ?></div>
            
            <?php
            $has_amp_after = strpos($rewritten_html, '&amp;') !== false;
            $status_class = $has_amp_after ? 'error' : 'success';
            ?>
            <div class="test-section <?php echo $status_class; ?>">
                <strong>重写结果:</strong> 
                <?php echo $has_amp_after ? '仍包含&amp;实体编码' : '已正确处理&amp;实体编码'; ?>
            </div>
        </div>

        <!-- SEO优化测试 -->
        <div class="test-section">
            <h2>4. SEO优化测试</h2>
            <?php
            $test_content = '<a href="?mod=siteinfo&amp;wid=177">网站详情</a>';
            $optimized_content = optimize_urls_for_seo($test_content);
            ?>
            
            <h3>优化前:</h3>
            <div class="code"><?php echo htmlspecialchars($test_content); ?></div>
            
            <h3>优化后:</h3>
            <div class="code"><?php echo htmlspecialchars($optimized_content); ?></div>
            
            <?php
            $is_optimized = !strpos($optimized_content, '&amp;') || $is_bot;
            $status_class = $is_optimized ? 'success' : 'warning';
            ?>
            <div class="test-section <?php echo $status_class; ?>">
                <strong>优化结果:</strong> 
                <?php 
                if ($is_bot) {
                    echo '搜索引擎爬虫 - URL已优化';
                } elseif ($is_optimized) {
                    echo '普通用户 - 保持HTML标准格式';
                } else {
                    echo '需要进一步优化';
                }
                ?>
            </div>
        </div>

        <!-- 实际URL测试 -->
        <div class="test-section">
            <h2>5. 实际URL访问测试</h2>
            <p>以下是一些实际的URL，可以点击测试是否正常工作：</p>
            <ul>
                <?php foreach ($test_cases as $type => $url): ?>
                <li>
                    <strong><?php echo htmlspecialchars($type); ?>:</strong> 
                    <a href="<?php echo htmlspecialchars($url); ?>" target="_blank">
                        <?php echo htmlspecialchars($url); ?>
                    </a>
                </li>
                <?php endforeach; ?>
            </ul>
        </div>

        <!-- 修复建议 -->
        <div class="test-section">
            <h2>6. 修复状态总结</h2>
            <?php
            $all_urls_clean = true;
            foreach ($test_cases as $url) {
                if (strpos($url, '&amp;') !== false) {
                    $all_urls_clean = false;
                    break;
                }
            }
            
            $status_class = $all_urls_clean ? 'success' : 'warning';
            ?>
            <div class="test-section <?php echo $status_class; ?>">
                <?php if ($all_urls_clean): ?>
                    <h3>✅ 修复成功</h3>
                    <p>所有URL生成函数都已正确处理，不再包含&amp;实体编码问题。</p>
                    <p>谷歌爬虫现在应该能够正确索引您的网站页面。</p>
                <?php else: ?>
                    <h3>⚠️ 需要进一步修复</h3>
                    <p>仍有部分URL包含&amp;实体编码，建议：</p>
                    <ul>
                        <li>清理所有模板缓存</li>
                        <li>检查模板文件中的URL生成代码</li>
                        <li>确保URL重写函数正确工作</li>
                    </ul>
                <?php endif; ?>
            </div>
        </div>

        <!-- 操作建议 -->
        <div class="test-section">
            <h2>7. 后续操作建议</h2>
            <ol>
                <li><strong>清理缓存:</strong> 删除 data/cache/ 和 data/compile/ 目录下的所有文件</li>
                <li><strong>Google Search Console:</strong> 请求重新抓取问题页面</li>
                <li><strong>监控日志:</strong> 检查服务器日志确认Googlebot访问正常</li>
                <li><strong>等待索引:</strong> 通常24-48小时内会看到索引改善</li>
            </ol>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <p><a href="system/url_fix.php">返回URL修复工具</a> | <a href="./">返回首页</a></p>
        </div>
    </div>
</body>
</html>
