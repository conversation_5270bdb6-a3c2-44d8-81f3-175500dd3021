<?php
/*
 * 测试规范URL修复效果
 * 用于验证重复网页问题是否已解决
 */

if (!defined('IN_IWEBDIR')) define('IN_IWEBDIR', true);

require_once('source/init.php');
require_once('source/module/prelink.php');
require_once('source/module/rewrite.php');

echo "<!DOCTYPE html>\n";
echo "<html>\n<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<title>规范URL修复测试</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; }\n";
echo ".test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }\n";
echo ".success { color: green; }\n";
echo ".error { color: red; }\n";
echo ".info { color: blue; }\n";
echo "table { border-collapse: collapse; width: 100%; }\n";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n";
echo "th { background-color: #f2f2f2; }\n";
echo "</style>\n";
echo "</head>\n<body>\n";

echo "<h1>规范URL修复测试结果</h1>\n";

// 测试1: URL生成函数测试
echo "<div class='test-section'>\n";
echo "<h2>1. URL生成函数测试</h2>\n";
echo "<table>\n";
echo "<tr><th>功能</th><th>生成的URL</th><th>状态</th></tr>\n";

$test_urls = array(
    '首页' => get_canonical_url('index'),
    '分类页面' => get_canonical_url('webdir', array('cid' => 1)),
    '分类页面(带分页)' => get_canonical_url('webdir', array('cid' => 1, 'page' => 2)),
    '归档页面' => get_canonical_url('archives', array('date' => '202501')),
    '归档页面(带分页)' => get_canonical_url('archives', array('date' => '202501', 'page' => 2)),
    '更新页面' => get_canonical_url('update', array('days' => 30)),
    '更新页面(带分页)' => get_canonical_url('update', array('days' => 30, 'page' => 2)),
    '站点详情' => get_canonical_url('siteinfo', array('wid' => 123)),
    '文章详情' => get_canonical_url('artinfo', array('aid' => 456)),
    '搜索页面' => get_canonical_url('search', array('type' => 'name', 'query' => 'test')),
    '搜索页面(带分页)' => get_canonical_url('search', array('type' => 'name', 'query' => 'test', 'page' => 2)),
);

foreach ($test_urls as $name => $url) {
    $status = !empty($url) ? "<span class='success'>✓ 正常</span>" : "<span class='error'>✗ 失败</span>";
    echo "<tr><td>$name</td><td>$url</td><td>$status</td></tr>\n";
}

echo "</table>\n";
echo "</div>\n";

// 测试2: .htaccess重定向规则测试
echo "<div class='test-section'>\n";
echo "<h2>2. .htaccess重定向规则测试</h2>\n";
echo "<p class='info'>以下URL应该被重定向到clean URL（需要在浏览器中测试）：</p>\n";
echo "<ul>\n";

$redirect_tests = array(
    '/archives/202501-1.html?mod=archives' => '/archives/202501-1.html',
    '/update/30-1.html?mod=update' => '/update/30-1.html',
    '/webdir/category/1-1.html?mod=webdir' => '/webdir/category/1-1.html',
    '/siteinfo/123.html?mod=siteinfo' => '/siteinfo/123.html',
    '/artinfo/456.html?mod=artinfo' => '/artinfo/456.html',
    '/?mod=index' => '/',
);

foreach ($redirect_tests as $from => $to) {
    echo "<li><strong>从:</strong> <code>$from</code> <strong>到:</strong> <code>$to</code></li>\n";
}

echo "</ul>\n";
echo "</div>\n";

// 测试3: 当前请求URL分析
echo "<div class='test-section'>\n";
echo "<h2>3. 当前请求URL分析</h2>\n";
echo "<table>\n";
echo "<tr><th>参数</th><th>值</th></tr>\n";
echo "<tr><td>REQUEST_URI</td><td>" . htmlspecialchars($_SERVER['REQUEST_URI']) . "</td></tr>\n";
echo "<tr><td>QUERY_STRING</td><td>" . htmlspecialchars($_SERVER['QUERY_STRING']) . "</td></tr>\n";
echo "<tr><td>HTTP_HOST</td><td>" . htmlspecialchars($_SERVER['HTTP_HOST']) . "</td></tr>\n";
echo "<tr><td>HTTPS</td><td>" . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'Yes' : 'No') . "</td></tr>\n";
echo "</table>\n";
echo "</div>\n";

// 测试4: 配置检查
echo "<div class='test-section'>\n";
echo "<h2>4. 系统配置检查</h2>\n";
echo "<table>\n";
echo "<tr><th>配置项</th><th>值</th><th>状态</th></tr>\n";

$link_struct = $options['link_struct'] ?? 0;
$link_struct_names = array(
    0 => '查询参数模式',
    1 => '路径模式1',
    2 => '路径模式2',
    3 => '路径模式3'
);

$link_struct_status = $link_struct > 0 ? "<span class='success'>✓ 已启用URL重写</span>" : "<span class='error'>✗ 未启用URL重写</span>";

echo "<tr><td>URL重写模式</td><td>" . ($link_struct_names[$link_struct] ?? '未知') . " ($link_struct)</td><td>$link_struct_status</td></tr>\n";
echo "<tr><td>站点根路径</td><td>" . htmlspecialchars($options['site_root']) . "</td><td><span class='info'>ℹ 信息</span></td></tr>\n";
echo "<tr><td>站点URL</td><td>" . htmlspecialchars($options['site_url']) . "</td><td><span class='info'>ℹ 信息</span></td></tr>\n";

echo "</table>\n";
echo "</div>\n";

// 测试5: 建议和说明
echo "<div class='test-section'>\n";
echo "<h2>5. 修复说明和建议</h2>\n";
echo "<h3>已实施的修复措施：</h3>\n";
echo "<ul>\n";
echo "<li><strong>✓ .htaccess重定向规则</strong> - 自动将带查询参数的clean URL重定向到纯clean URL</li>\n";
echo "<li><strong>✓ PHP规范URL强制</strong> - 在PHP层面检查并重定向非规范URL</li>\n";
echo "<li><strong>✓ 模板canonical标签</strong> - 更新模板输出正确的canonical URL</li>\n";
echo "<li><strong>✓ robots.txt优化</strong> - 禁止搜索引擎抓取重复URL</li>\n";
echo "</ul>\n";

echo "<h3>测试建议：</h3>\n";
echo "<ul>\n";
echo "<li>1. 访问带查询参数的clean URL，检查是否自动重定向</li>\n";
echo "<li>2. 检查网站页面的canonical标签是否正确</li>\n";
echo "<li>3. 使用Google Search Console检查重复内容问题</li>\n";
echo "<li>4. 清理缓存并重新生成sitemap</li>\n";
echo "</ul>\n";

echo "<h3>预期效果：</h3>\n";
echo "<ul>\n";
echo "<li>• 解决Google索引编制中的\"重复网页，用户未选定规范网页\"问题</li>\n";
echo "<li>• 统一URL格式，避免同一内容多个URL</li>\n";
echo "<li>• 提升SEO效果，避免权重分散</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<p><strong>测试完成时间：</strong> " . date('Y-m-d H:i:s') . "</p>\n";
echo "<p><a href='?' style='color: #007bff;'>刷新测试</a> | <a href='/' style='color: #007bff;'>返回首页</a></p>\n";
echo "</div>\n";

echo "</body>\n</html>\n";
?>
