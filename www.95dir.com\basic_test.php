<?php
// 最基本的PHP测试
echo "PHP is working!<br>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Current Time: " . date('Y-m-d H:i:s') . "<br>";

// 测试错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Error reporting enabled<br>";

// 测试文件存在性
$files_to_check = array(
    './source/init.php',
    './source/include/function.php',
    './config.php'
);

echo "<h3>File Check:</h3>";
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✓ $file exists<br>";
    } else {
        echo "✗ $file missing<br>";
    }
}

echo "<h3>Test Complete</h3>";
?>
