<?php
/**
 * 全新的站点地图生成系统
 * 支持标准sitemap协议、分页、缓存等功能
 */
if (!defined('IN_IWEBDIR')) exit('Access Denied');

// 确保包含必要的模块文件
if (!function_exists('get_website_url')) {
    require_once('./source/module/prelink.php');
}
if (!function_exists('get_one_category')) {
    require_once('./source/module/category.php');
}
if (!function_exists('get_sitemap_cache')) {
    require_once('./source/module/sitemap_cache.php');
}

// 获取参数
$type = trim(isset($_GET['type']) ? $_GET['type'] : '');
$page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
$cate_id = intval(isset($_GET['cid']) ? $_GET['cid'] : 0);

// 默认生成主索引
if (empty($type)) $type = 'index';

// 站点地图配置
$sitemap_config = array(
    'max_urls_per_sitemap' => 50000,  // 每个站点地图最大URL数量
    'cache_time' => 3600,             // 缓存时间（秒）
    'enable_cache' => true,           // 是否启用缓存
    'cache_dir' => APP_PATH . 'data/cache/sitemap/',  // 缓存目录
);

// 确保缓存目录存在
if ($sitemap_config['enable_cache'] && !is_dir($sitemap_config['cache_dir'])) {
    @mkdir($sitemap_config['cache_dir'], 0755, true);
}

// 路由到相应的处理函数
switch ($type) {
    case 'index':
        generate_sitemap_index();
        break;
    case 'websites':
        generate_websites_sitemap($page, $cate_id);
        break;
    case 'articles':
        generate_articles_sitemap($page, $cate_id);
        break;
    case 'categories':
        generate_categories_sitemap();
        break;
    case 'pages':
        generate_pages_sitemap();
        break;
    case 'tags':
        generate_tags_sitemap();
        break;
    default:
        // 兼容旧版本URL
        if ($type == 'webdir' || $type == 'weblink' || $type == 'all') {
            generate_websites_sitemap($page, $cate_id);
        } elseif ($type == 'article') {
            generate_articles_sitemap($page, $cate_id);
        } elseif ($type == 'category') {
            generate_categories_sitemap();
        } elseif ($type == 'vip') {
            generate_websites_sitemap($page, 0, true);
        } else {
            generate_sitemap_index();
        }
        break;
}

/**
 * 生成站点地图索引文件
 */
function generate_sitemap_index() {
    global $DB, $options, $sitemap_config;

    $cache_key = 'sitemap_index';
    $cache_file = $sitemap_config['cache_dir'] . $cache_key . '.xml';

    // 检查缓存
    if ($sitemap_config['enable_cache'] && file_exists($cache_file) &&
        (time() - filemtime($cache_file)) < $sitemap_config['cache_time']) {
        header('Content-Type: application/xml; charset=utf-8');
        readfile($cache_file);
        return;
    }

    // 开始生成XML
    ob_start();

    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

    $base_url = rtrim($options['site_url'], '/');
    $current_time = date('c');

    // 计算各类型内容的数量和分页
    $websites_count = get_websites_count();
    $articles_count = get_articles_count();

    // 网站站点地图
    $websites_pages = ceil($websites_count / $sitemap_config['max_urls_per_sitemap']);
    for ($i = 1; $i <= $websites_pages; $i++) {
        echo "    <sitemap>\n";
        echo "        <loc>" . xml_escape($base_url . "?mod=sitemap&amp;type=websites&amp;page=" . $i) . "</loc>\n";
        echo "        <lastmod>" . $current_time . "</lastmod>\n";
        echo "    </sitemap>\n";
    }

    // 文章站点地图
    $articles_pages = ceil($articles_count / $sitemap_config['max_urls_per_sitemap']);
    for ($i = 1; $i <= $articles_pages; $i++) {
        echo "    <sitemap>\n";
        echo "        <loc>" . xml_escape($base_url . "?mod=sitemap&amp;type=articles&amp;page=" . $i) . "</loc>\n";
        echo "        <lastmod>" . $current_time . "</lastmod>\n";
        echo "    </sitemap>\n";
    }

    // 分类站点地图
    echo "    <sitemap>\n";
    echo "        <loc>" . xml_escape($base_url . "?mod=sitemap&amp;type=categories") . "</loc>\n";
    echo "        <lastmod>" . $current_time . "</lastmod>\n";
    echo "    </sitemap>\n";

    // 页面站点地图
    echo "    <sitemap>\n";
    echo "        <loc>" . xml_escape($base_url . "?mod=sitemap&amp;type=pages") . "</loc>\n";
    echo "        <lastmod>" . $current_time . "</lastmod>\n";
    echo "    </sitemap>\n";

    // 标签站点地图
    echo "    <sitemap>\n";
    echo "        <loc>" . xml_escape($base_url . "?mod=sitemap&amp;type=tags") . "</loc>\n";
    echo "        <lastmod>" . $current_time . "</lastmod>\n";
    echo "    </sitemap>\n";

    echo '</sitemapindex>' . "\n";

    $content = ob_get_contents();
    ob_end_clean();

    // 保存缓存
    if ($sitemap_config['enable_cache']) {
        file_put_contents($cache_file, $content);
    }

    // 输出内容
    header('Content-Type: application/xml; charset=utf-8');
    echo $content;
}

/**
 * 生成网站站点地图
 */
function generate_websites_sitemap($page = 1, $cate_id = 0, $vip_only = false) {
    global $DB, $options, $sitemap_config;

    $page = max(1, $page);
    $offset = ($page - 1) * $sitemap_config['max_urls_per_sitemap'];

    // 生成缓存键
    $cache_key = 'sitemap_websites_' . $page . '_' . $cate_id . '_' . ($vip_only ? '1' : '0');
    $cache_file = $sitemap_config['cache_dir'] . $cache_key . '.xml';

    // 检查缓存
    if ($sitemap_config['enable_cache'] && file_exists($cache_file) &&
        (time() - filemtime($cache_file)) < $sitemap_config['cache_time']) {
        header('Content-Type: application/xml; charset=utf-8');
        readfile($cache_file);
        return;
    }

    // 构建查询条件
    $where = "w.web_status=3";

    // 检查违规状态字段是否存在
    $table_name = $DB->table('websites');
    $check_sql = "SHOW COLUMNS FROM `{$table_name}` LIKE 'web_violation_status'";
    $check_result = $DB->query($check_sql);
    $has_violation_field = $DB->num_rows($check_result) > 0;

    if ($has_violation_field) {
        $where .= " AND (w.web_violation_status IS NULL OR w.web_violation_status=0)";
    }

    if ($vip_only) {
        $where .= " AND w.web_ispay=1";
    }

    if ($cate_id > 0) {
        $cate = get_one_category($cate_id);
        if (!empty($cate)) {
            if ($cate['cate_childcount'] > 0) {
                $where .= " AND w.cate_id IN (".$cate['cate_arrchildid'].")";
            } else {
                $where .= " AND w.cate_id=$cate_id";
            }
        }
    }

    // 查询数据
    $sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_ctime, w.web_utime, d.web_utime as data_utime
            FROM " . $DB->table('websites') . " w
            LEFT JOIN " . $DB->table('webdata') . " d ON w.web_id=d.web_id
            WHERE $where
            ORDER BY w.web_id DESC
            LIMIT $offset, " . $sitemap_config['max_urls_per_sitemap'];

    $query = $DB->query($sql);
    $results = array();
    while ($row = $DB->fetch_array($query)) {
        $row['web_link'] = get_website_url($row['web_id'], true, $row['web_name'], $row['web_url']);
        $row['lastmod'] = max($row['web_ctime'], $row['web_utime'] ?: 0, $row['data_utime'] ?: 0);
        $results[] = $row;
    }
    $DB->free_result($query);

    // 开始生成XML
    ob_start();

    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

    // 如果是第一页，添加首页和主要页面
    if ($page == 1 && $cate_id == 0 && !$vip_only) {
        // 首页
        echo "    <url>\n";
        echo "        <loc>" . xml_escape($options['site_url']) . "</loc>\n";
        echo "        <lastmod>" . date('c') . "</lastmod>\n";
        echo "        <changefreq>daily</changefreq>\n";
        echo "        <priority>1.0</priority>\n";
        echo "    </url>\n";

        // 主要页面
        $main_pages = array(
            'webdir' => array('priority' => '0.8', 'changefreq' => 'daily'),
            'article' => array('priority' => '0.8', 'changefreq' => 'daily'),
            'category' => array('priority' => '0.7', 'changefreq' => 'weekly'),
            'feedback' => array('priority' => '0.4', 'changefreq' => 'monthly'),
            'link' => array('priority' => '0.5', 'changefreq' => 'weekly')
        );

        foreach ($main_pages as $page_mod => $config) {
            echo "    <url>\n";
            echo "        <loc>" . xml_escape($options['site_url'] . "?mod=" . $page_mod) . "</loc>\n";
            echo "        <lastmod>" . date('c') . "</lastmod>\n";
            echo "        <changefreq>" . $config['changefreq'] . "</changefreq>\n";
            echo "        <priority>" . $config['priority'] . "</priority>\n";
            echo "    </url>\n";
        }
    }

    // 添加网站URL
    foreach ($results as $row) {
        echo "    <url>\n";
        echo "        <loc>" . xml_escape($row['web_link']) . "</loc>\n";
        echo "        <lastmod>" . date('c', $row['lastmod']) . "</lastmod>\n";
        echo "        <changefreq>weekly</changefreq>\n";
        echo "        <priority>0.6</priority>\n";
        echo "    </url>\n";
    }

    echo '</urlset>' . "\n";

    $content = ob_get_contents();
    ob_end_clean();

    // 保存缓存
    if ($sitemap_config['enable_cache']) {
        file_put_contents($cache_file, $content);
    }

    // 输出内容
    header('Content-Type: application/xml; charset=utf-8');
    echo $content;
}

/**
 * 生成文章站点地图
 */
function generate_articles_sitemap($page = 1, $cate_id = 0) {
    global $DB, $options, $sitemap_config;

    $page = max(1, $page);
    $offset = ($page - 1) * $sitemap_config['max_urls_per_sitemap'];

    // 生成缓存键
    $cache_key = 'sitemap_articles_' . $page . '_' . $cate_id;
    $cache_file = $sitemap_config['cache_dir'] . $cache_key . '.xml';

    // 检查缓存
    if ($sitemap_config['enable_cache'] && file_exists($cache_file) &&
        (time() - filemtime($cache_file)) < $sitemap_config['cache_time']) {
        header('Content-Type: application/xml; charset=utf-8');
        readfile($cache_file);
        return;
    }

    // 构建查询条件
    $where = "a.art_status=3";

    if ($cate_id > 0) {
        $cate = get_one_category($cate_id);
        if (!empty($cate)) {
            if ($cate['cate_childcount'] > 0) {
                $where .= " AND a.cate_id IN (".$cate['cate_arrchildid'].")";
            } else {
                $where .= " AND a.cate_id=$cate_id";
            }
        }
    }

    // 查询数据
    $sql = "SELECT a.art_id, a.art_title, a.art_ctime, a.art_utime
            FROM " . $DB->table('articles') . " a
            WHERE $where
            ORDER BY a.art_id DESC
            LIMIT $offset, " . $sitemap_config['max_urls_per_sitemap'];

    $query = $DB->query($sql);
    $results = array();
    while ($row = $DB->fetch_array($query)) {
        $row['art_link'] = get_article_url($row['art_id'], true, $row['art_title']);
        $row['lastmod'] = max($row['art_ctime'], $row['art_utime'] ?: 0);
        $results[] = $row;
    }
    $DB->free_result($query);

    // 开始生成XML
    ob_start();

    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

    // 添加文章URL
    foreach ($results as $row) {
        echo "    <url>\n";
        echo "        <loc>" . xml_escape($row['art_link']) . "</loc>\n";
        echo "        <lastmod>" . date('c', $row['lastmod']) . "</lastmod>\n";
        echo "        <changefreq>monthly</changefreq>\n";
        echo "        <priority>0.5</priority>\n";
        echo "    </url>\n";
    }

    echo '</urlset>' . "\n";

    $content = ob_get_contents();
    ob_end_clean();

    // 保存缓存
    if ($sitemap_config['enable_cache']) {
        file_put_contents($cache_file, $content);
    }

    // 输出内容
    header('Content-Type: application/xml; charset=utf-8');
    echo $content;
}

/**
 * 生成分类站点地图
 */
function generate_categories_sitemap() {
    global $DB, $options, $sitemap_config;

    // 生成缓存键
    $cache_key = 'sitemap_categories';
    $cache_file = $sitemap_config['cache_dir'] . $cache_key . '.xml';

    // 检查缓存
    if ($sitemap_config['enable_cache'] && file_exists($cache_file) &&
        (time() - filemtime($cache_file)) < $sitemap_config['cache_time']) {
        header('Content-Type: application/xml; charset=utf-8');
        readfile($cache_file);
        return;
    }

    // 开始生成XML
    ob_start();

    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

    // 获取所有分类
    $sql = "SELECT cate_id, cate_mod, cate_name, cate_dir FROM " . $DB->table('categories') . " ORDER BY cate_id";
    $query = $DB->query($sql);

    while ($row = $DB->fetch_array($query)) {
        $category_url = get_category_url($row['cate_mod'], $row['cate_id'], 1, true);

        echo "    <url>\n";
        echo "        <loc>" . xml_escape($category_url) . "</loc>\n";
        echo "        <lastmod>" . date('c') . "</lastmod>\n";
        echo "        <changefreq>weekly</changefreq>\n";
        echo "        <priority>0.8</priority>\n";
        echo "    </url>\n";
    }
    $DB->free_result($query);

    echo '</urlset>' . "\n";

    $content = ob_get_contents();
    ob_end_clean();

    // 保存缓存
    if ($sitemap_config['enable_cache']) {
        file_put_contents($cache_file, $content);
    }

    // 输出内容
    header('Content-Type: application/xml; charset=utf-8');
    echo $content;
}

/**
 * 生成页面站点地图
 */
function generate_pages_sitemap() {
    global $DB, $options, $sitemap_config;

    // 生成缓存键
    $cache_key = 'sitemap_pages';
    $cache_file = $sitemap_config['cache_dir'] . $cache_key . '.xml';

    // 检查缓存
    if ($sitemap_config['enable_cache'] && file_exists($cache_file) &&
        (time() - filemtime($cache_file)) < $sitemap_config['cache_time']) {
        header('Content-Type: application/xml; charset=utf-8');
        readfile($cache_file);
        return;
    }

    // 开始生成XML
    ob_start();

    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

    // 获取自定义页面
    $sql = "SELECT page_id, page_title FROM " . $DB->table('diypages') . " WHERE page_status=1 ORDER BY page_id";
    $query = $DB->query($sql);

    while ($row = $DB->fetch_array($query)) {
        $page_url = $options['site_url'] . "?mod=diypage&pid=" . $row['page_id'];

        echo "    <url>\n";
        echo "        <loc>" . xml_escape($page_url) . "</loc>\n";
        echo "        <lastmod>" . date('c') . "</lastmod>\n";
        echo "        <changefreq>monthly</changefreq>\n";
        echo "        <priority>0.3</priority>\n";
        echo "    </url>\n";
    }
    $DB->free_result($query);

    // 添加其他功能页面
    $other_pages = array(
        'top' => array('priority' => '0.6', 'changefreq' => 'daily'),
        'update' => array('priority' => '0.5', 'changefreq' => 'daily'),
        'archives' => array('priority' => '0.4', 'changefreq' => 'weekly'),
        'search' => array('priority' => '0.3', 'changefreq' => 'weekly')
    );

    foreach ($other_pages as $page_mod => $config) {
        echo "    <url>\n";
        echo "        <loc>" . xml_escape($options['site_url'] . "?mod=" . $page_mod) . "</loc>\n";
        echo "        <lastmod>" . date('c') . "</lastmod>\n";
        echo "        <changefreq>" . $config['changefreq'] . "</changefreq>\n";
        echo "        <priority>" . $config['priority'] . "</priority>\n";
        echo "    </url>\n";
    }

    echo '</urlset>' . "\n";

    $content = ob_get_contents();
    ob_end_clean();

    // 保存缓存
    if ($sitemap_config['enable_cache']) {
        file_put_contents($cache_file, $content);
    }

    // 输出内容
    header('Content-Type: application/xml; charset=utf-8');
    echo $content;
}

/**
 * 生成标签站点地图
 */
function generate_tags_sitemap() {
    global $DB, $options, $sitemap_config;

    // 生成缓存键
    $cache_key = 'sitemap_tags';
    $cache_file = $sitemap_config['cache_dir'] . $cache_key . '.xml';

    // 检查缓存
    if ($sitemap_config['enable_cache'] && file_exists($cache_file) &&
        (time() - filemtime($cache_file)) < $sitemap_config['cache_time']) {
        header('Content-Type: application/xml; charset=utf-8');
        readfile($cache_file);
        return;
    }

    // 开始生成XML
    ob_start();

    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

    // 收集网站标签
    $all_tags = array();
    $sql = "SELECT web_tags FROM " . $DB->table('websites') . " WHERE web_status=3 AND web_tags!=''";
    $query = $DB->query($sql);

    while ($row = $DB->fetch_array($query)) {
        $tags = explode(',', $row['web_tags']);
        foreach ($tags as $tag) {
            $tag = trim($tag);
            if (!empty($tag)) {
                $all_tags[$tag] = isset($all_tags[$tag]) ? $all_tags[$tag] + 1 : 1;
            }
        }
    }
    $DB->free_result($query);

    // 收集文章标签
    $sql = "SELECT art_tags FROM " . $DB->table('articles') . " WHERE art_status=3 AND art_tags!=''";
    $query = $DB->query($sql);

    while ($row = $DB->fetch_array($query)) {
        $tags = explode(',', $row['art_tags']);
        foreach ($tags as $tag) {
            $tag = trim($tag);
            if (!empty($tag)) {
                $all_tags[$tag] = isset($all_tags[$tag]) ? $all_tags[$tag] + 1 : 1;
            }
        }
    }
    $DB->free_result($query);

    // 按使用频率排序，取前50个热门标签
    arsort($all_tags);
    $hot_tags = array_slice($all_tags, 0, 50, true);

    foreach ($hot_tags as $tag => $count) {
        if ($count >= 2) { // 至少被使用2次的标签才加入sitemap
            $tag_url = $options['site_url'] . '?mod=search&type=tags&query=' . urlencode($tag);
            echo "    <url>\n";
            echo "        <loc>" . xml_escape($tag_url) . "</loc>\n";
            echo "        <lastmod>" . date('c') . "</lastmod>\n";
            echo "        <changefreq>weekly</changefreq>\n";
            echo "        <priority>0.4</priority>\n";
            echo "    </url>\n";
        }
    }

    echo '</urlset>' . "\n";

    $content = ob_get_contents();
    ob_end_clean();

    // 保存缓存
    if ($sitemap_config['enable_cache']) {
        file_put_contents($cache_file, $content);
    }

    // 输出内容
    header('Content-Type: application/xml; charset=utf-8');
    echo $content;
}

/**
 * 获取网站总数
 */
function get_websites_count($vip_only = false) {
    global $DB;

    $where = "web_status=3";

    // 检查违规状态字段是否存在
    $table_name = $DB->table('websites');
    $check_sql = "SHOW COLUMNS FROM `{$table_name}` LIKE 'web_violation_status'";
    $check_result = $DB->query($check_sql);
    $has_violation_field = $DB->num_rows($check_result) > 0;

    if ($has_violation_field) {
        $where .= " AND (web_violation_status IS NULL OR web_violation_status=0)";
    }

    if ($vip_only) {
        $where .= " AND web_ispay=1";
    }

    return $DB->get_count($DB->table('websites'), $where);
}

/**
 * 获取文章总数
 */
function get_articles_count() {
    global $DB;

    $where = "art_status=3";
    return $DB->get_count($DB->table('articles'), $where);
}

/**
 * 清除站点地图缓存
 */
function clear_sitemap_cache($type = 'all') {
    global $sitemap_config;

    if (!$sitemap_config['enable_cache'] || !is_dir($sitemap_config['cache_dir'])) {
        return false;
    }

    $cache_dir = $sitemap_config['cache_dir'];

    if ($type == 'all') {
        // 清除所有缓存
        $files = glob($cache_dir . '*.xml');
        foreach ($files as $file) {
            @unlink($file);
        }
    } else {
        // 清除特定类型的缓存
        $pattern = $cache_dir . 'sitemap_' . $type . '*.xml';
        $files = glob($pattern);
        foreach ($files as $file) {
            @unlink($file);
        }
    }

    return true;
}

/**
 * 获取站点地图统计信息
 */
function get_sitemap_stats() {
    global $sitemap_config;

    $stats = array(
        'websites_count' => get_websites_count(),
        'articles_count' => get_articles_count(),
        'cache_enabled' => $sitemap_config['enable_cache'],
        'cache_files' => 0,
        'cache_size' => 0
    );

    if ($sitemap_config['enable_cache'] && is_dir($sitemap_config['cache_dir'])) {
        $files = glob($sitemap_config['cache_dir'] . '*.xml');
        $stats['cache_files'] = count($files);

        foreach ($files as $file) {
            $stats['cache_size'] += filesize($file);
        }
    }

    return $stats;
}

?>