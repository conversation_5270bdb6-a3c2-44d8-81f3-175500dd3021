<?php
/**
 * 简单的站点地图测试页面
 */
define('IN_IWEBDIR', true);
define('APP_PATH', './');

// 基本初始化
require_once('./source/init.php');

// 包含必要的模块
require_once('./source/module/prelink.php');
require_once('./source/module/category.php');
require_once('./source/module/website.php');
require_once('./source/module/article.php');
require_once('./source/module/sitemap_cache.php');

echo "<h1>站点地图测试页面</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

// 测试函数是否存在
echo "<h2>函数检查</h2>";
$functions = array(
    'get_website_url' => '网站URL生成函数',
    'get_article_url' => '文章URL生成函数', 
    'get_category_url' => '分类URL生成函数',
    'xml_escape' => 'XML转义函数',
    'get_one_category' => '获取分类函数',
    'get_sitemap_cache' => '缓存函数'
);

foreach ($functions as $func => $desc) {
    if (function_exists($func)) {
        echo "<p class='success'>✓ {$desc}: {$func}</p>";
    } else {
        echo "<p class='error'>✗ {$desc}: {$func}</p>";
    }
}

// 测试数据库连接
echo "<h2>数据库测试</h2>";
try {
    $websites_count = $DB->get_count($DB->table('websites'), "web_status=3");
    $articles_count = $DB->get_count($DB->table('articles'), "art_status=3");
    echo "<p class='success'>✓ 数据库连接正常</p>";
    echo "<p class='info'>网站数量: {$websites_count}</p>";
    echo "<p class='info'>文章数量: {$articles_count}</p>";
} catch (Exception $e) {
    echo "<p class='error'>✗ 数据库错误: " . $e->getMessage() . "</p>";
}

// 测试URL生成
echo "<h2>URL生成测试</h2>";
if (function_exists('get_website_url')) {
    try {
        $test_url = get_website_url(1, true);
        echo "<p class='success'>✓ 网站URL: {$test_url}</p>";
    } catch (Exception $e) {
        echo "<p class='error'>✗ 网站URL生成失败: " . $e->getMessage() . "</p>";
    }
}

if (function_exists('get_article_url')) {
    try {
        $test_url = get_article_url(1, true);
        echo "<p class='success'>✓ 文章URL: {$test_url}</p>";
    } catch (Exception $e) {
        echo "<p class='error'>✗ 文章URL生成失败: " . $e->getMessage() . "</p>";
    }
}

// 测试站点地图链接
echo "<h2>站点地图链接</h2>";
$base_url = $options['site_url'];
echo "<p><a href='{$base_url}?mod=sitemap' target='_blank'>站点地图索引</a></p>";
echo "<p><a href='{$base_url}?mod=sitemap&type=websites' target='_blank'>网站站点地图</a></p>";
echo "<p><a href='{$base_url}?mod=sitemap&type=articles' target='_blank'>文章站点地图</a></p>";
echo "<p><a href='{$base_url}?mod=sitemap&type=categories' target='_blank'>分类站点地图</a></p>";

// 测试缓存
echo "<h2>缓存测试</h2>";
try {
    $cache = get_sitemap_cache();
    $stats = $cache->getStats();
    echo "<p class='success'>✓ 缓存系统正常</p>";
    echo "<p class='info'>缓存文件数: {$stats['total_files']}</p>";
    echo "<p class='info'>有效缓存: {$stats['valid_files']}</p>";
} catch (Exception $e) {
    echo "<p class='error'>✗ 缓存系统错误: " . $e->getMessage() . "</p>";
}

echo "<h2>测试完成</h2>";
echo "<p>如果上述测试都通过，站点地图功能应该可以正常工作。</p>";
?>
